create table bank_accounts.airwallex_bank_account
(
  guid                 uuid
    constraint pkey__airwallex_bank_account primary key,
  created_at           timestamptz not null default now(),
  updated_at           timestamptz not null default now(),

  business_guid        uuid        not null,
  airwallex_account_id text        not null,
  airwallex_wallet_id  text,

  ach_routing_number   text,
  ach_account_number   text,
  wire_routing_number  text,
  wire_account_number  text,
  iban                 text,
  swift_code           text,
  enabled              boolean     not null default true,

  constraint uniq__airwallex_bank_account__business_guid
    unique (business_guid)
);

create trigger on_update__airwallex_bank_account
  before update
  on bank_accounts.airwallex_bank_account
  for each row
execute procedure updated();
