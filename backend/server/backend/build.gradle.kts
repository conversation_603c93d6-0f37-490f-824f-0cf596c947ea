plugins {
  id("highbeam-jvm")
  id("highbeam-jvm-server")
}

dependencies {
  implementation(project(":common:airwallex"))
  implementation(project(":common:auth0"))
  implementation(project(":common:aws"))
  implementation(project(":common:backend-v2"))
  implementation(project(":common:email"))
  implementation(project(":common:event"))
  implementation(project(":common:google-cloud-storage"))
  implementation(project(":common:job-runner"))
  implementation(project(":common:notion"))
  implementation(project(":common:rutter"))
  implementation(project(":common:sentry-feature"))
  implementation(project(":common:server"))
  implementation(project(":common:shopify"))
  implementation(project(":common:slack"))
  implementation(project(":common:sql"))
  implementation(project(":common:taktile"))
  implementation(project(":common:task"))
  implementation(project(":common:unit-co"))
  implementation(project(":db:highbeam"))
  implementation(project(":module:backend-v2-email:module"))
  implementation(project(":module:business:business:module"))
  implementation(project(":module:business:details:module"))
  implementation(project(":module:capital:capital-reporting:module"))
  implementation(project(":module:capital:transaction:module"))
  implementation(project(":module:capital:charge-card:module"))
  implementation(project(":module:capital:credit:module"))
  implementation(project(":module:capital:account:module"))
  implementation(project(":module:capital:repayment:module"))
  implementation(project(":module:capital:repayment-schedule:module"))
  implementation(project(":module:connect:module"))
  implementation(project(":module:evaluations:module"))
  implementation(project(":module:health-check:client"))
  implementation(project(":module:health-check:module"))
  implementation(project(":module:highbeam:module"))
  implementation(project(":module:intercom:module"))
  implementation(project(":module:job:module"))
  implementation(project(":module:metrics:module"))
  implementation(project(":module:onboarding:module"))
  implementation(project(":module:payment:gateway:module"))
  implementation(project(":module:transfer:module"))
  implementation(project(":module:treasury:module"))
  implementation(project(":module:user:module"))
  implementation(Dependencies.Sql.jdbi3Kotlin)
  implementation(Dependencies.Sql.jdbi3Postgres)

  testImplementation(project(":common:integration-testing"))
}
