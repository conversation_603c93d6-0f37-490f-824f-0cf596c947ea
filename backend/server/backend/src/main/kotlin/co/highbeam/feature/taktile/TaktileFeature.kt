package co.highbeam.feature.taktile

import co.highbeam.feature.Feature
import com.google.inject.name.Names
import com.taktile.client.TaktileClient
import com.taktile.client.TaktileProvider
import com.taktile.config.TaktileConfig

const val TAKTILE_FEATURE = "taktile"

internal class TaktileFeature(
  private val taktileConfig: TaktileConfig,
) : Feature() {
  override fun bind() {
    bind(TaktileConfig::class.java).toInstance(taktileConfig)

    bind(TaktileClient::class.java)
      .toProvider(TaktileProvider::class.java)
      .asEagerSingleton()

    bind(TaktileClient::class.java).annotatedWith(Names.named(TAKTILE_FEATURE))
      .toProvider(TaktileProvider::class.java)
      .asEagerSingleton()
  }
}
