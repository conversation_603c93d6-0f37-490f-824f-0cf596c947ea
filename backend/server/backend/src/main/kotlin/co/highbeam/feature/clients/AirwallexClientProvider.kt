package co.highbeam.feature.clients

import co.highbeam.config.AirwallexConfig
import co.highbeam.metrics.Metrics
import com.airwallex.client.AirwallexClient
import com.airwallex.client.AirwallexHttpClient
import com.google.inject.Inject
import com.google.inject.Provider
import java.time.Clock

class AirwallexClientProvider @Inject constructor(
  private val metrics: Metrics,
  private val clock: Clock,
  private val airwallexConfig: AirwallexConfig,
) : Provider<AirwallexClient> {
  override fun get(): AirwallexClient {
    val httpClient = AirwallexHttpClient(
      environment = airwallexConfig.environment,
      metrics = metrics,
    )
    return AirwallexClient(
      httpClient = httpClient,
      metrics = metrics,
      apiKey = airwallexConfig.apiKey,
      clientId = airwallexConfig.clientId,
      clock = clock,
      onBehalfOf = null, // Default client doesn't act on behalf of anyone
    )
  }
}
