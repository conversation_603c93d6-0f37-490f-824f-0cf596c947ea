# This config is used when running the app locally but connecting to the staging database.
#
# To use this config locally, you must set:
# - HIGH<PERSON>AM_POSTGRES_JDBC_URL to the staging database URL
# - HIGHBEAM_POSTGRES_USERNAME to your Highbeam email address

app:
  appBaseUrl: http://localhost:3000
  apiBaseUrl: http://localhost:8080

airwallex:
  environment: Production
  apiKey:
    type: Plaintext
    value: production-airwallex-api-key
  clientId: production-airwallex-client-id
  slackWebhookPath: productionAirwallexSlackWebhookPath

auth0:
  baseUrl: https://highbeam.us.auth0.com
  clientId: p8P6Bg69AFdm7xZ2tAp3UjvXrPvDo2VC
  clientSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_AUTH0_CLIENT_SECRET
    defaultValue: ****************************************************************

aws: { }

backendV2:
  baseUrl: https://v2.api.highbeam.co
  jwtMechanism:
    source: Static
    issuer: https://highbeam.co/
    algorithm: Hmac256
    secret:
      type: GcpSecret
      projectId: highbeam-production
      secretId: jwt-secret
      versionId: latest

bankingConfig:
  slackWebhooks:
    monitoring: triggers/T01GWSTDAN6/*************/a677a0eef03315a9f268739907523297
  promotionAprilReferralGuids:
    - 7bb969ae-5386-4bd8-b079-5fdd2e1852a8

businessMetrics:
  enabled: false

clock:
  type: Real

credit:
  lineOfCreditOfferAcceptedSlackWebhookPath: workflows/T01GWSTDAN6/A04TS9XRH70/******************/uiHP2mLMfRJ5ls3qwVhFky74
  requestBankVerificationLetterSlackWebhookPath: triggers/T01GWSTDAN6/*************/f7aa8e2a8f5bf9dcb00433c4c1283efd
  lineOfCreditApprovalProcessedSlackWebhookPath: workflows/T01GWSTDAN6/A05AT1M9HEG/******************/fBRHaRacGHbJrS4ptqqusDs3
  lineOfCreditGenericMessageSlackWebhookPath: workflows/T01GWSTDAN6/A05CAPBGM4J/******************/plxCo5jxdmwxwVrKoNofWdzc
  lineOfCreditAppSubmittedSlackWebhookPath: workflows/T01GWSTDAN6/A058QBA0THV/******************/ACNiODP16c4uMkTHFAOkneiP
  highbeamSpvCollectionUnitAccountId: 3837698
  highbeamOldFundingUnitAccountId: 1569875
  highbeamThreadLineOfCreditCounterpartyId: 1
  mustacheTemplateRoot: templates
  externalLenderLineOfCreditUnitAccountId:
    Ember: 3139972
    Paperstack: 3992357

chargeCard:
  chargeCardWebhookPath: services/T01GWSTDAN6/B07CPN1RPE0/Ft7FE4H52tUl0IIdO1LlYOZi
  highbeamThreadChargeCardRepaymentUnitAccountId: 392395
  highbeamSpvCollectionUnitAccountId: 3837698

creditTransaction:
  highbeamFundingHighYieldSavingUnitAccountId: 3837661
  highbeamSpvCollectionUnitAccountId: 3837698
  highbeamOldFundingUnitAccountId: 1569875
  oldFundingAccounts:
    - 392395
  capitalDrawdownWebhookPath: workflows/T01GWSTDAN6/A05A6BBNKG8/******************/u03mlGg6gJgkQmhyeoK7MBQy
  capitalDrawdownApprovalWebhookPath: services/T01GWSTDAN6/B07CR31F5QD/GeD6izA4bbGXVfPOYn6SHKy5
  capitalExternalLenderDrawdownWebhookPath: triggers/T01GWSTDAN6/*************/ffa3100a377b87a281a846054119a9cc
  externalLenderLineOfCreditUnitAccountId:
    Ember: 3139972
    Paperstack: 3992357
  loanTapeSubledgerUnitAccountIds:
    - 3656243

currencyCloud:
  environment: Demo
  loginId: <EMAIL>
  apiKey:
    type: Plaintext
    value: 2ec02394428fb2a806255ce2ed7012f63a49dd1d57e434c328fc5770041b4ef3
  webhookKey:
    type: Plaintext
    value: invalid-key
  slackWebhookPath: workflows/T01GWSTDAN6/A042XFMG89M/******************/T8qEhY1apXgpahMjjJIr9Dot
  paymentFeeId: 6db5717e-2f9c-4775-ab4d-d3651d14820d

email:
  enabled: false

event:
  enabled:
    listening: false
    publishing: false

featureFlags:
  launchDarklySdkKey:
    type: EnvironmentVariable
    name: HIGHBEAM_LAUNCH_DARKLY_SDK_KEY
    defaultValue: ****************************************

fraudMonitor:
  cardAuthorizationSlackWebhookPath: workflows/T01GWSTDAN6/A051D6MBA5D/******************/9YJHNgOQdHc6Rf5CqIzniy5i
  cardStatusSlackWebhookPath: workflows/T01GWSTDAN6/A051U37EFLG/******************/ql5ncpiMB77dbcfNs9n3wwei

# Uncomment this config if GCS is required (e.g. invoices)
# Need to set GOOGLE_APPLICATION_CREDENTIALS environment variable to file containing API credentials
#googleCloudStorage:
#  gcpProjectId: highbeam-staging
#  trustedBucketName: assets.staging.highbeam.co
#  untrustedBucketName: unscanned-assets.staging.highbeam.co
#  internalBucketName: internal.staging.highbeam.co
#  urlSigningDuration: 900 # 15 minutes

hashing:
  internalDataHashSecret:
    type: Plaintext
    value: highbeam

highbeamDatabase:
  jdbcUrl:
    type: EnvironmentVariable
    name: HIGHBEAM_PRODUCTION_POSTGRES_JDBC_URL
  username:
    type: EnvironmentVariable
    name: HIGHBEAM_PRODUCTION_POSTGRES_USERNAME
  password:
    type: Command
    command: gcloud auth print-access-token
  runMigrations: false
  connectionTimeout: 2500
  maximumPoolSize: 2

hosts:
  backend: http://localhost:8080

intercom:
  secret:
    type: EnvironmentVariable
    name: HIGHBEAM_INTERCOM_SECRET
  unitCoAdminUrl: https://app.unit.co

jobsDatabase:
  jdbcUrl:
    type: Plaintext
    value: ************************************
  schema: quartz
  username:
    type: Plaintext
    value: highbeam
  password:
    type: Plaintext
  startDelaySeconds: 0

metrics: { }

name: backend

notion:
  baseUrl: https://api.notion.com/v1
  databaseId: cc45b5295dce4b3bba3b4fe3a26c3bde
  notionVersion: 2021-08-16
  secret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: notion-secret
    versionId: latest

plaid:
  environment: Sandbox
  webhookUrl: https://api.staging.highbeam.co/plaid/webhook
  clientId: 60b91c131c29f40010af12ec
  secret:
    type: Plaintext
    value: 503f14e8ac9db7352e75f947cbd41c
  enrichAccountsAsync: false

rest:
  authentication:
    verifiers:
      - type: Jwt
        mechanisms:
          - source: Static
            issuer: https://highbeam.co/
            leeway: 300 # 5 minutes
            algorithm: Hmac256
            secret:
              type: Plaintext
              value: highbeam
          - source: Jwk
            issuer: https://auth.highbeam.co/
            url: https://highbeam.us.auth0.com/.well-known/jwks.json
          - source: Jwk
            issuer: accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
          - source: Jwk
            issuer: https://accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
      - type: Token
        internalToken:
          token:
            type: Plaintext
            value: internal-token
          roles: [HIGHBEAM_SERVER]
        tokens:
          - token:
              type: Plaintext
              value: cloud-tasks-token
            roles: [CLOUD_TASKS]
  port: 8080
  parallelization:
    connectionGroupSize: 6
    workerGroupSize: 12
    callGroupSize: 48

# The Rutter integration won't work locally unless HIGHBEAM_RUTTER_CLIENT_ID and
# HIGHBEAM_RUTTER_API_SECRET are set. Visit https://dashboard.rutterapi.com to get real values
# and set them as environment variables.
rutter:
  clientId:
    type: EnvironmentVariable
    name: HIGHBEAM_RUTTER_CLIENT_ID
    defaultValue: dummy_client_id
  apiSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_RUTTER_API_SECRET
    defaultValue: dummy_api_secret
  baseUrl: https://production.rutterapi.com/versioned
  version: 2023-03-14

sentry:
  dsn: ''
  environment: ''

# The Shopify integration won't work locally unless HIGHBEAM_SHOPIFY_API_KEY and
# HIGHBEAM_SHOPIFY_API_SECRET are set. Visit https://accounts.shopify.com to get real values
# and set them as environment variables.
shopify:
  apiKey:
    type: EnvironmentVariable
    name: HIGHBEAM_SHOPIFY_API_KEY
    defaultValue: dummy_api_key
  apiSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_SHOPIFY_API_SECRET
    defaultValue: dummy_api_secret
  apiScopes: read_analytics,read_all_orders,read_assigned_fulfillment_orders,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,read_discounts,read_inventory,read_locations,read_marketing_events,read_orders,read_payment_terms,read_products,read_returns,read_shipping,read_shopify_payments_disputes,read_shopify_payments_payouts,read_customers
  webhookAddress: https://api.highbeam.co/shopify-webhook
  mandatoryWebhookSlackWebhookPath: workflows/fake-mandatory-webhook-slack-webhook-path

task:
  enabled: true
  projectName: highbeam-production
  locationName: us-central1
  cloudSchedulerRequestEmail: <EMAIL>
  cloudSchedulerRequestAudience: backend-v1-tasks
  cloudTasksToken:
    type: GcpSecret
    projectId: highbeam-production
    secretId: cloud-tasks-token # corresponds to CLOUD_TASKS platform role
    versionId: latest

taktile:
  environment: Sandbox
  apiKey:
    type: EnvironmentVariable
    name: HIGHBEAM_TAKTILE_API_KEY
    defaultValue: dummy_api_key
  flowSlug:
    type: Plaintext
    value: middesk-kyb-flow
  flowVersion:
    type: Plaintext
    value: "1.0"

unitCo:
  environment: Production
  secret:
    type: EnvironmentVariable
    name: HIGHBEAM_UNIT_CO_SECRET
    defaultValue: v2.public.eyJyb2xlIjoiYWRtaW4iLCJ1c2VySWQiOiIyMjAxIiwic3ViIjoic2l2YUBoaWdoYmVhbS5jbyIsImV4cCI6IjIwMjMtMTEtMTFUMTY6MjM6MjUuOTQ3WiIsImp0aSI6IjE5NTg4MCIsIm9yZ0lkIjoiNTUzIiwic2NvcGUiOiJhcHBsaWNhdGlvbnMgYXBwbGljYXRpb25zLXdyaXRlIGN1c3RvbWVycyBjdXN0b21lcnMtd3JpdGUgY3VzdG9tZXItdGFncy13cml0ZSBjdXN0b21lci10b2tlbi13cml0ZSBhY2NvdW50cyBhY2NvdW50cy13cml0ZSBjYXJkcyBjYXJkcy13cml0ZSBjYXJkcy1zZW5zaXRpdmUgY2FyZHMtc2Vuc2l0aXZlLXdyaXRlIHRyYW5zYWN0aW9ucyB0cmFuc2FjdGlvbnMtd3JpdGUgYXV0aG9yaXphdGlvbnMgc3RhdGVtZW50cyBwYXltZW50cyBwYXltZW50cy13cml0ZSBwYXltZW50cy13cml0ZS1jb3VudGVycGFydHkgcmVwYXltZW50cyByZXBheW1lbnRzLXdyaXRlIHBheW1lbnRzLXdyaXRlLWFjaC1kZWJpdCBjb3VudGVycGFydGllcyBjb3VudGVycGFydGllcy13cml0ZSBiYXRjaC1yZWxlYXNlcyBiYXRjaC1yZWxlYXNlcy13cml0ZSB3ZWJob29rcyB3ZWJob29rcy13cml0ZSBldmVudHMgZXZlbnRzLXdyaXRlIGF1dGhvcml6YXRpb24tcmVxdWVzdHMgYXV0aG9yaXphdGlvbi1yZXF1ZXN0cy13cml0ZSBjaGVjay1kZXBvc2l0cyBjaGVjay1kZXBvc2l0cy13cml0ZSByZWNlaXZlZC1wYXltZW50cyByZWNlaXZlZC1wYXltZW50cy13cml0ZSBkaXNwdXRlcyBjaGFyZ2ViYWNrcyBjaGFyZ2ViYWNrcy13cml0ZSByZXdhcmRzIHJld2FyZHMtd3JpdGUiLCJvcmciOiJIaWdoYmVhbSIsInNvdXJjZUlwIjoiIiwidXNlclR5cGUiOiJvcmciLCJpc1VuaXRQaWxvdCI6ZmFsc2V9l1MMCvR9UBiF3san3f23ms4gJ3Jg0hTnh42NwTar8zU-mNWqNTQf9CDz6EI6D-HpZ6NWpshs-yPzc_t7AQ9QDA
  webhookToken:
    type: EnvironmentVariable
    name: HIGHBEAM_UNIT_CO_WEBHOOK_TOKEN
    defaultValue: xvzigaa5eb60uq2t0hp3gtypmcjtk8ulksg2m3q5qk6fg5jxiapvchqncuebpc15

uuids:
  generation: Random
