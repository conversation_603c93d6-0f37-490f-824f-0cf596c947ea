# This config is used in the REAL STAGING environment.

app:
  appBaseUrl: https://app.staging.highbeam.co
  apiBaseUrl: https://api.staging.highbeam.co

airwallex:
  environment: Sandbox
  apiKey:
    type: Plaintext
    value: staging-airwallex-api-key
  clientId: staging-airwallex-client-id
  slackWebhookPath: workflows/airwallex/local-staging/slack-url

auth0:
  baseUrl: https://highbeam-staging.us.auth0.com
  clientId: StQ9rKQsan7ty0CPCEB43QEuL248RkIO
  clientSecret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: auth0-client-secret
    versionId: latest

aws:
  credentials:
    accessKeyId: AKIATUVULIWSGRXAJAYB
    secretAccessKey:
      type: GcpSecret
      projectId: highbeam-staging
      secretId: transfer_user_aws_secret_key # TODO: This should be snake-case.
      versionId: latest

backendV2:
  baseUrl: https://v2.api.staging.highbeam.co
  jwtMechanism:
    source: Static
    issuer: https://highbeam.co/
    algorithm: Hmac256
    secret:
      type: GcpSecret
      projectId: highbeam-staging
      secretId: jwt-secret
      versionId: latest

bankingConfig:
  slackWebhooks:
    monitoring: triggers/T01GWSTDAN6/*************/a677a0eef03315a9f268739907523297
  promotionAprilReferralGuids:
    - 6ea57eb8-7da3-40dd-a0f2-1e8c370fdee2

businessMetrics:
  enabled: false

clock:
  type: Real

credit:
  lineOfCreditOfferAcceptedSlackWebhookPath: workflows/line-of-credit-accepted/test/slack-url
  requestBankVerificationLetterSlackWebhookPath: request-bank-verification-letter/test/slack-url
  lineOfCreditApprovalProcessedSlackWebhookPath: workflows/line-of-credit-approval-processed/test/slack-url
  lineOfCreditGenericMessageSlackWebhookPath: workflows/line-of-credit-generic-message/test/slack-url
  lineOfCreditAppSubmittedSlackWebhookPath: workflows/line-of-credit-app-submitted/test/slack-url
  highbeamSpvCollectionUnitAccountId: 5645309
  highbeamOldFundingUnitAccountId: 1544691
  highbeamThreadLineOfCreditCounterpartyId: 458356
  mustacheTemplateRoot: templates
  externalLenderLineOfCreditUnitAccountId:
    Dwight: 4493761
    Ember: 4493761
    Paperstack: 5665008
    Sandbox: 2477508
    SandboxDemo: 4493761
    Uncapped: 5039830

chargeCard:
  chargeCardWebhookPath: charge-card/test/slack-url
  highbeamThreadChargeCardRepaymentUnitAccountId: 1544691
  highbeamSpvCollectionUnitAccountId: 5645309

creditTransaction:
  highbeamFundingHighYieldSavingUnitAccountId: 392395
  highbeamSpvCollectionUnitAccountId: 5645309
  highbeamOldFundingUnitAccountId: 1544691
  oldFundingAccounts:
    - 1544691
  capitalDrawdownWebhookPath: workflows/line-of-credit-drawdown/test/slack-url
  capitalDrawdownApprovalWebhookPath: workflows/line-of-credit-drawdown/test/slack-url
  capitalExternalLenderDrawdownWebhookPath: line-of-credit-drawdown/test/slack-url
  externalLenderLineOfCreditUnitAccountId:
    Dwight: 4493761
    Ember: 4493761
    Paperstack: 5665008
    Sandbox: 2477508
    SandboxDemo: 4493761
    Uncapped: 5039830
  loanTapeSubledgerUnitAccountIds:
    - 5647300

currencyCloud:
  environment: Demo
  loginId: <EMAIL>
  apiKey:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: currency-cloud-api-key
    versionId: latest
  webhookKey:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: currency-cloud-webhook-token
    versionId: latest
  slackWebhookPath: workflows/T01GWSTDAN6/A042XFMG89M/******************/T8qEhY1apXgpahMjjJIr9Dot
  paymentFeeId: 6db5717e-2f9c-4775-ab4d-d3651d14820d

email:
  enabled: true
  sendgridApiKey:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: sendgrid-api-key
    versionId: latest
  sendgridTemplateId: d-d05050e75dd341618653d7428a8bb148

event:
  enabled:
    listening: false
    publishing: true
  project: highbeam-staging

featureFlags:
  launchDarklySdkKey:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: launch-darkly-sdk-key
    versionId: latest

fraudMonitor:
  cardAuthorizationSlackWebhookPath: workflows/T01GWSTDAN6/A051TKMDAE7/******************/tXBiqg7lmhgPS1GxlUHTMf9d
  cardStatusSlackWebhookPath: workflows/T01GWSTDAN6/A051M253T9N/******************/BhF5SyBPLCvDlW4biUn6PjNg

googleCloudStorage:
  gcpProjectId: highbeam-staging
  trustedBucketName: assets.staging.highbeam.co
  untrustedBucketName: unscanned-assets.staging.highbeam.co
  internalBucketName: internal.staging.highbeam.co
  urlSigningDuration: 900 # 15 minutes

hashing:
  internalDataHashSecret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: internal-data-hash-secret
    versionId: latest

highbeamDatabase:
  jdbcUrl:
    type: EnvironmentVariable
    name: HIGHBEAM_POSTGRES_JDBC_URL
  username:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: postgres-username
    versionId: latest
  password:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: postgres-password
    versionId: latest
  runMigrations: true
  connectionTimeout: 2500
  minimumIdle: 8
  maximumPoolSize: 32

hosts:
  backend: http://highbeam-backend

intercom:
  secret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: intercom-secret
    versionId: latest
  unitCoAdminUrl: https://app.s.unit.sh

jobsDatabase:
  jdbcUrl:
    type: EnvironmentVariable
    name: HIGHBEAM_POSTGRES_JDBC_URL
  schema: quartz
  username:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: postgres-username
    versionId: latest
  password:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: postgres-password
    versionId: latest
  startDelaySeconds: 15

metrics:
  datadog:
    environment: staging
    uri: https://api.us5.datadoghq.com
    apiKey:
      type: GcpSecret
      projectId: highbeam-staging
      secretId: datadog-api-key
      versionId: latest
    applicationKey:
      type: GcpSecret
      projectId: highbeam-staging
      secretId: datadog-application-key
      versionId: latest

name: backend

notion:
  baseUrl: https://api.notion.com/v1
  databaseId: cbcb2556907c47fcb1301306d6649dbe
  notionVersion: 2021-08-16
  secret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: notion-secret
    versionId: latest

plaid:
  environment: Sandbox
  webhookUrl: https://api.staging.highbeam.co/plaid/webhook
  clientId: 60b91c131c29f40010af12ec
  secret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: plaid-secret
    versionId: latest

rest:
  authentication:
    verifiers:
      - type: Jwt
        mechanisms:
          - source: Static
            issuer: https://highbeam.co/
            algorithm: Hmac256
            secret:
              type: GcpSecret
              projectId: highbeam-staging
              secretId: jwt-secret
              versionId: latest
          - source: Jwk
            issuer: https://auth.staging.highbeam.co/
            url: https://highbeam-staging.us.auth0.com/.well-known/jwks.json
          - source: Jwk
            issuer: accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
          - source: Jwk
            issuer: https://accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
      - type: Token
        internalToken:
          token:
            type: GcpSecret
            projectId: highbeam-staging
            secretId: internal-token
            versionId: latest
          roles: [HIGHBEAM_SERVER]
        tokens:
          - token:
              type: GcpSecret
              projectId: highbeam-staging
              secretId: auth0-token
              versionId: latest
            roles: [IDENTITY_PROVIDER]
          - token:
              type: GcpSecret
              projectId: highbeam-staging
              secretId: cloud-tasks-token
              versionId: latest
            roles: [CLOUD_TASKS]
  port: 8080
  parallelization:
    connectionGroupSize: 32
    workerGroupSize: 64
    callGroupSize: 256

rutter:
  clientId:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: rutter-client-id
    versionId: latest
  apiSecret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: rutter-api-secret
    versionId: latest
  # We use production api because Sandbox doesn't have all functionalities.
  # Highbeam doesn't perform any write operations so this shouldn't impact
  # production accounts.
  baseUrl: https://production.rutterapi.com/versioned
  version: 2023-03-14

sentry:
  dsn: https://<EMAIL>/6175439
  environment: staging

shopify:
  apiKey:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: shopify-api-key
    versionId: latest
  apiSecret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: shopify-api-secret
    versionId: latest
  apiScopes: read_analytics,read_all_orders,read_assigned_fulfillment_orders,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,read_discounts,read_inventory,read_locations,read_marketing_events,read_orders,read_payment_terms,read_products,read_returns,read_shipping,read_shopify_payments_disputes,read_shopify_payments_payouts,read_customers
  webhookAddress: https://api.staging.highbeam.co/shopify-webhook
  mandatoryWebhookSlackWebhookPath: workflows/fake-mandatory-webhook-slack-webhook-path

task:
  enabled: true
  projectName: highbeam-staging
  locationName: us-central1
  cloudSchedulerRequestEmail: <EMAIL>
  cloudSchedulerRequestAudience: backend-v1-tasks
  cloudTasksToken:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: cloud-tasks-token # corresponds to CLOUD_TASKS platform role
    versionId: latest

taktile:
  environment: Sandbox
  apiKey:
    type: GcpSecret
    projectId: highbeam-production
    secretId: taktile-api-key
    versionId: latest
  flowSlug:
    type: Plaintext
    value: middesk-kyb-flow
  flowVersion:
    type: Plaintext
    value: "1.0"

unitCo:
  environment: Sandbox
  secret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: unit-co-secret
    versionId: latest
  webhookToken:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: unit-co-webhook-token
    versionId: latest

uuids:
  generation: Random
