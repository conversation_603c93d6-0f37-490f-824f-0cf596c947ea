# This config is used when running the app locally but connecting to the staging database.
#
# To use this config locally, you must set:
# - HIGH<PERSON>AM_POSTGRES_JDBC_URL to the staging database URL
# - HIGHBEAM_POSTGRES_USERNAME to your Highbeam email address

app:
  appBaseUrl: http://localhost:3000
  apiBaseUrl: http://localhost:8080

airwallex:
  environment: Sandbox
  apiKey:
    type: Plaintext
    value: staging-airwallex-api-key
  clientId: staging-airwallex-client-id
  slackWebhookPath: workflows/airwallex/local-staging/slack-url

auth0:
  baseUrl: https://highbeam-staging.us.auth0.com
  clientId: StQ9rKQsan7ty0CPCEB43QEuL248RkIO
  clientSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_AUTH0_CLIENT_SECRET
    defaultValue: ****************************************************************

aws: { }

backendV2:
  baseUrl: https://v2.api.staging.highbeam.co
  jwtMechanism:
    source: Static
    issuer: https://highbeam.co/
    algorithm: Hmac256
    secret:
      type: GcpSecret
      projectId: highbeam-staging
      secretId: jwt-secret
      versionId: latest

bankingConfig:
  slackWebhooks:
    monitoring: triggers/T01GWSTDAN6/*************/a677a0eef03315a9f268739907523297
  promotionAprilReferralGuids:
    - 6ea57eb8-7da3-40dd-a0f2-1e8c370fdee2

businessMetrics:
  enabled: false

clock:
  type: Real

credit:
  lineOfCreditOfferAcceptedSlackWebhookPath: workflows/line-of-credit-accepted/test/slack-url
  requestBankVerificationLetterSlackWebhookPath: request-bank-verification-letter/test/slack-url
  lineOfCreditApprovalProcessedSlackWebhookPath: workflows/line-of-credit-approval-processed/test/slack-url
  lineOfCreditGenericMessageSlackWebhookPath: workflows/line-of-credit-generic-message/test/slack-url
  lineOfCreditAppSubmittedSlackWebhookPath: workflows/line-of-credit-app-submitted/test/slack-url
  highbeamSpvCollectionUnitAccountId: 5645309
  highbeamOldFundingUnitAccountId: 1544691
  highbeamThreadLineOfCreditCounterpartyId: 458356
  mustacheTemplateRoot: templates
  externalLenderLineOfCreditUnitAccountId:
    Dwight: 4493761
    Ember: 4493761
    Paperstack: 5665008
    Sandbox: 2477508
    SandboxDemo: 4493761
    Uncapped: 5039830

chargeCard:
  chargeCardWebhookPath: charge-card/test/slack-url
  highbeamThreadChargeCardRepaymentUnitAccountId: 1544691
  highbeamSpvCollectionUnitAccountId: 5645309

creditTransaction:
  highbeamFundingHighYieldSavingUnitAccountId: 392395
  highbeamSpvCollectionUnitAccountId: 5645309
  highbeamOldFundingUnitAccountId: 1544691
  oldFundingAccounts:
    - 1544691
  capitalDrawdownWebhookPath: workflows/line-of-credit-drawdown/test/slack-url
  capitalDrawdownApprovalWebhookPath: workflows/line-of-credit-drawdown/test/slack-url
  capitalExternalLenderDrawdownWebhookPath: line-of-credit-drawdown/test/slack-url
  externalLenderLineOfCreditUnitAccountId:
    Dwight: 4493761
    Ember: 4493761
    Paperstack: 5665008
    Sandbox: 2477508
    SandboxDemo: 4493761
    Uncapped: 5039830
  loanTapeSubledgerUnitAccountIds:
    - 5647300

currencyCloud:
  environment: Demo
  loginId: <EMAIL>
  apiKey:
    type: Plaintext
    value: 2ec02394428fb2a806255ce2ed7012f63a49dd1d57e434c328fc5770041b4ef3
  webhookKey:
    type: Plaintext
    value: invalid-key
  slackWebhookPath: workflows/T01GWSTDAN6/A042XFMG89M/******************/T8qEhY1apXgpahMjjJIr9Dot
  paymentFeeId: 6db5717e-2f9c-4775-ab4d-d3651d14820d

email:
  enabled: false

event:
  enabled:
    listening: false
    publishing: false

featureFlags:
  launchDarklySdkKey:
    type: EnvironmentVariable
    name: HIGHBEAM_LAUNCH_DARKLY_SDK_KEY
    defaultValue: ****************************************

fraudMonitor:
  cardAuthorizationSlackWebhookPath: workflows/T01GWSTDAN6/A051TKMDAE7/******************/tXBiqg7lmhgPS1GxlUHTMf9d
  cardStatusSlackWebhookPath: workflows/T01GWSTDAN6/A051M253T9N/******************/BhF5SyBPLCvDlW4biUn6PjNg

# Uncomment this config if GCS is required (e.g. invoices)
# Need to set GOOGLE_APPLICATION_CREDENTIALS environment variable to file containing API credentials
#googleCloudStorage:
#  gcpProjectId: highbeam-staging
#  trustedBucketName: assets.staging.highbeam.co
#  untrustedBucketName: unscanned-assets.staging.highbeam.co
#  internalBucketName: internal.staging.highbeam.co
#  urlSigningDuration: 900 # 15 minutes

hashing:
  internalDataHashSecret:
    type: Plaintext
    value: highbeam

highbeamDatabase:
  jdbcUrl:
    type: EnvironmentVariable
    name: HIGHBEAM_POSTGRES_JDBC_URL
  username:
    type: EnvironmentVariable
    name: HIGHBEAM_POSTGRES_USERNAME
  password:
    type: Command
    command: gcloud auth print-access-token
  runMigrations: false
  connectionTimeout: 2500
  maximumPoolSize: 2

hosts:
  backend: http://localhost:8080

intercom:
  secret:
    type: EnvironmentVariable
    name: HIGHBEAM_INTERCOM_SECRET
  unitCoAdminUrl: https://app.s.unit.sh

jobsDatabase:
  jdbcUrl:
    type: Plaintext
    value: ************************************
  schema: quartz
  username:
    type: Plaintext
    value: highbeam
  password:
    type: Plaintext
  startDelaySeconds: 0

metrics: { }

name: backend

notion:
  baseUrl: https://api.notion.com/v1
  databaseId: cbcb2556907c47fcb1301306d6649dbe
  notionVersion: 2021-08-16
  secret:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: notion-secret
    versionId: latest

plaid:
  environment: Sandbox
  webhookUrl: https://api.staging.highbeam.co/plaid/webhook
  clientId: 60b91c131c29f40010af12ec
  secret:
    type: Plaintext
    value: 503f14e8ac9db7352e75f947cbd41c
  enrichAccountsAsync: false

rest:
  authentication:
    verifiers:
      - type: Jwt
        mechanisms:
          - source: Static
            issuer: https://highbeam.co/
            leeway: 300 # 5 minutes
            algorithm: Hmac256
            secret:
              type: Plaintext
              value: highbeam
          - source: Jwk
            issuer: https://auth.staging.highbeam.co/
            url: https://highbeam-staging.us.auth0.com/.well-known/jwks.json
          - source: Jwk
            issuer: accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
          - source: Jwk
            issuer: https://accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
      - type: Token
        internalToken:
          token:
            type: Plaintext
            value: internal-token
          roles: [HIGHBEAM_SERVER]
        tokens:
          - token:
              type: Plaintext
              value: cloud-tasks-token
            roles: [CLOUD_TASKS]
  port: 8080
  parallelization:
    connectionGroupSize: 6
    workerGroupSize: 12
    callGroupSize: 48

# The Rutter integration won't work locally unless HIGHBEAM_RUTTER_CLIENT_ID and
# HIGHBEAM_RUTTER_API_SECRET are set. Visit https://dashboard.rutterapi.com to get real values
# and set them as environment variables.
rutter:
  clientId:
    type: EnvironmentVariable
    name: HIGHBEAM_RUTTER_CLIENT_ID
    defaultValue: dummy_client_id
  apiSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_RUTTER_API_SECRET
    defaultValue: dummy_api_secret
  baseUrl: https://production.rutterapi.com/versioned
  version: 2023-03-14

sentry:
  dsn: ''
  environment: ''

# The Taktile integration won't work locally unless HIGHBEAM_TAKTILE_API_KEY is set.
# Visit Taktile dashboard to get real values and set them as environment variables.
taktile:
  environment: Sandbox
  apiKey:
    type: EnvironmentVariable
    name: HIGHBEAM_TAKTILE_API_KEY
    defaultValue: dummy_api_key
  flowSlug:
    type: Plaintext
    value: middesk-kyb-flow
  flowVersion:
    type: Plaintext
    value: "1.0"

# The Shopify integration won't work locally unless HIGHBEAM_SHOPIFY_API_KEY and
# HIGHBEAM_SHOPIFY_API_SECRET are set. Visit https://accounts.shopify.com to get real values
# and set them as environment variables.
shopify:
  apiKey:
    type: EnvironmentVariable
    name: HIGHBEAM_SHOPIFY_API_KEY
    defaultValue: dummy_api_key
  apiSecret:
    type: EnvironmentVariable
    name: HIGHBEAM_SHOPIFY_API_SECRET
    defaultValue: dummy_api_secret
  apiScopes: read_analytics,read_all_orders,read_assigned_fulfillment_orders,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,read_discounts,read_inventory,read_locations,read_marketing_events,read_orders,read_payment_terms,read_products,read_returns,read_shipping,read_shopify_payments_disputes,read_shopify_payments_payouts,read_customers
  webhookAddress: https://api.staging.highbeam.co/shopify-webhook
  mandatoryWebhookSlackWebhookPath: workflows/fake-mandatory-webhook-slack-webhook-path

task:
  enabled: true
  projectName: highbeam-staging
  locationName: us-central1
  cloudSchedulerRequestEmail: <EMAIL>
  cloudSchedulerRequestAudience: backend-v1-tasks
  cloudTasksToken:
    type: GcpSecret
    projectId: highbeam-staging
    secretId: cloud-tasks-token # corresponds to CLOUD_TASKS platform role
    versionId: latest

unitCo:
  environment: Sandbox
  secret:
    type: EnvironmentVariable
    name: HIGHBEAM_UNIT_CO_SECRET
    defaultValue: v2.public.eyJyb2xlIjoiYWRtaW4iLCJ1c2VySWQiOiIyMjAxIiwic3ViIjoic2l2YUBoaWdoYmVhbS5jbyIsImV4cCI6IjIwMjYtMDItMDZUMTU6MTg6MTcuMTc5WiIsImp0aSI6IjQwNzA0OCIsIm9yZ0lkIjoiNTUzIiwic2NvcGUiOiJhcHBsaWNhdGlvbnMgYXBwbGljYXRpb25zLXdyaXRlIGN1c3RvbWVycyBjdXN0b21lcnMtd3JpdGUgY3VzdG9tZXItdGFncy13cml0ZSBjdXN0b21lci10b2tlbi13cml0ZSBhY2NvdW50cyBhY2NvdW50cy13cml0ZSBhY2NvdW50LWhvbGRzIGFjY291bnQtaG9sZHMtd3JpdGUgY2FyZHMgY2FyZHMtd3JpdGUgY2FyZHMtc2Vuc2l0aXZlIGNhcmRzLXNlbnNpdGl2ZS13cml0ZSB0cmFuc2FjdGlvbnMgdHJhbnNhY3Rpb25zLXdyaXRlIGF1dGhvcml6YXRpb25zIHN0YXRlbWVudHMgcGF5bWVudHMgcGF5bWVudHMtd3JpdGUgcGF5bWVudHMtd3JpdGUtY291bnRlcnBhcnR5IHBheW1lbnRzLXdyaXRlLWxpbmtlZC1hY2NvdW50IGFjaC1wYXltZW50cy13cml0ZSB3aXJlLXBheW1lbnRzLXdyaXRlIHJlcGF5bWVudHMgcmVwYXltZW50cy13cml0ZSBwYXltZW50cy13cml0ZS1hY2gtZGViaXQgY291bnRlcnBhcnRpZXMgY291bnRlcnBhcnRpZXMtd3JpdGUgYmF0Y2gtcmVsZWFzZXMgYmF0Y2gtcmVsZWFzZXMtd3JpdGUgbGlua2VkLWFjY291bnRzIGxpbmtlZC1hY2NvdW50cy13cml0ZSB3ZWJob29rcyB3ZWJob29rcy13cml0ZSBldmVudHMgZXZlbnRzLXdyaXRlIGF1dGhvcml6YXRpb24tcmVxdWVzdHMgYXV0aG9yaXphdGlvbi1yZXF1ZXN0cy13cml0ZSBjYXNoLWRlcG9zaXRzIGNhc2gtZGVwb3NpdHMtd3JpdGUgY2hlY2stZGVwb3NpdHMgY2hlY2stZGVwb3NpdHMtd3JpdGUgcmVjZWl2ZWQtcGF5bWVudHMgcmVjZWl2ZWQtcGF5bWVudHMtd3JpdGUgZGlzcHV0ZXMgY2hhcmdlYmFja3MgY2hhcmdlYmFja3Mtd3JpdGUgcmV3YXJkcyByZXdhcmRzLXdyaXRlIGNoZWNrLXBheW1lbnRzIGNoZWNrLXBheW1lbnRzLXdyaXRlIGNyZWRpdC1kZWNpc2lvbnMgY3JlZGl0LWRlY2lzaW9ucy13cml0ZSBsZW5kaW5nLXByb2dyYW1zIGxlbmRpbmctcHJvZ3JhbXMtd3JpdGUgY2FyZC1mcmF1ZC1jYXNlcyBjYXJkLWZyYXVkLWNhc2VzLXdyaXRlIGNyZWRpdC1hcHBsaWNhdGlvbnMgY3JlZGl0LWFwcGxpY2F0aW9ucy13cml0ZSBtaWdyYXRpb25zIG1pZ3JhdGlvbnMtd3JpdGUgdGF4IHRheC13cml0ZSBmb3JtcyBmb3Jtcy13cml0ZSBmb3Jtcy1zZW5zaXRpdmUgd2lyZS1kcmF3ZG93bnMgd2lyZS1kcmF3ZG93bnMtd3JpdGUiLCJvcmciOiJIaWdoYmVhbSIsInNvdXJjZUlwIjoiIiwidXNlclR5cGUiOiJvcmciLCJpc1VuaXRQaWxvdCI6ZmFsc2UsImlzUGFyZW50T3JnIjpmYWxzZX0_NX0fx8ELTNjLMAX8v6RWQpm3b0qR5SGD7sDaZurafyFPAJpiNAFtAFCezkDIhMY-scRBy4PSPAqFrroSYGwP
  webhookToken:
    type: EnvironmentVariable
    name: HIGHBEAM_UNIT_CO_WEBHOOK_TOKEN
    defaultValue: xvzigaa5eb60uq2t0hp3gtypmcjtk8ulksg2m3q5qk6fg5jxiapvchqncuebpc15

uuids:
  generation: Random
