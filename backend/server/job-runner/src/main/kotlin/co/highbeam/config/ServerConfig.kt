package co.highbeam.config

import co.highbeam.businessMetrics.BusinessMetricsConfig
import co.highbeam.capital.chargeCard.config.ChargeCardConfig
import co.highbeam.capital.transaction.config.CapitalTransactionConfig
import co.highbeam.config.backendV2.BackendV2Config
import com.taktile.config.TaktileConfig
import highbeam.config.task.TaskConfig

internal data class ServerConfig(
  override val app: AppConfig,
  val airwallex: AirwallexConfig,
  val auth0: Auth0Config,
  val aws: AwsConfig,
  val backendV2: BackendV2Config,
  val bankingConfig: BankingConfig,
  val businessMetrics: BusinessMetricsConfig,
  override val clock: ClockConfig,
  val credit: CreditConfig,
  val creditTransaction: CapitalTransactionConfig,
  val chargeCard: ChargeCardConfig,
  val currencyCloud: CurrencyCloudConfig,
  val email: EmailConfig,
  val event: EventConfig,
  val featureFlags: FeatureFlagsConfig,
  val fraudMonitor: FraudMonitorConfig,
  val googleCloudStorage: GoogleCloudStorageConfig?,
  val highbeamDatabase: SqlDatabaseConfig,
  val hosts: Hosts,
  val jobs: JobsConfig,
  override val metrics: MetricsConfig,
  override val name: String,
  val notion: NotionConfig,
  val plaid: PlaidConfig,
  val rest: RestConfig,
  val rutter: RutterConfig,
  val sentry: SentryConfig,
  val shopify: ShopifyConfig,
  val taktile: TaktileConfig,
  val task: TaskConfig,
  val unitCo: UnitCoConfig,
  override val uuids: UuidsConfig,
) : Config()
