package com.taktile.rep

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DataWrapper<T>(
  val data: T,
  val metadata: Metadata,
) {
  data class Metadata(
    @JsonProperty("version") val version: String,
    @JsonProperty("entity_id") val entityId: String,
  )
}
