package com.taktile.client

import co.highbeam.client.RealHttpClient
import co.highbeam.metrics.Metrics
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.typeConversion.typeConverter.BalanceLongTypeConverter
import co.highbeam.typeConversion.typeConverter.BalanceStringTypeConverter
import co.highbeam.typeConversion.typeConverter.DEFAULT_TYPE_CONVERTERS
import co.highbeam.typeConversion.typeConverter.MoneyLongTypeConverter
import co.highbeam.typeConversion.typeConverter.MoneyStringTypeConverter
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies

private const val REQUEST_TIMEOUT_MILLIS = 60_000L

val taktileObjectMapper: ObjectMapper = HighbeamObjectMapper.json {
  useNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
  allowUnknownProperties(true)
  useTypeConverters(
    DEFAULT_TYPE_CONVERTERS - BalanceLongTypeConverter +
            BalanceStringTypeConverter - MoneyLongTypeConverter + MoneyStringTypeConverter
  )
}.build()

class TaktileHttpClient(
  environment: Environment,
  metrics: Metrics,
) : RealHttpClient(
  baseUrl = environment.baseUrl,
  metrics = metrics,
  objectMapper = taktileObjectMapper,
  requestTimeoutMillis = REQUEST_TIMEOUT_MILLIS,
  retry = true,
) {
  enum class Environment(val baseUrl: String) {
    Sandbox("https://api.taktile.com"),
    Production("https://api.taktile.com"),
  }
}
