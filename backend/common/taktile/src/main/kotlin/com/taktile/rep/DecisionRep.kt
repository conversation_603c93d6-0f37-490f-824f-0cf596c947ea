package com.taktile.rep

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.util.UUID

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionRequest<T>(
  val data: T,
  val metadata: DecisionMetadata,
  val control: DecisionControl? = null,
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionMetadata(
  val version: String? = null,
  @JsonProperty("entity_id") val entityId: String? = null,
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionControl(
  @JsonProperty("execution_mode") val executionMode: ExecutionMode = ExecutionMode.SYNC,
)

enum class ExecutionMode {
  @JsonProperty("sync")
  SYNC,

  @JsonProperty("async")
  ASYNC
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionResponse(
  @JsonProperty("business_guid") val businessGuid: UUID,
  @JsonProperty("credit_application_guid") val creditApplicationGuid: UUID,
  @JsonProperty("decision") val decision: Decision,
  @JsonProperty("rejected_reasons") val rejectedReasons: List<String>,
  @JsonProperty("middesk_business_creation_response") val middeskBusinessCreationResponse: JsonNode,
  @JsonProperty("middesk_report_unified") val middeskReportUnified: JsonNode,
) {
  enum class Decision {
    Accepted, Rejected
  }
}
