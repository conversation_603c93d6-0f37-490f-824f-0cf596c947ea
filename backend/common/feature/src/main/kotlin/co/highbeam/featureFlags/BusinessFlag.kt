package co.highbeam.featureFlags

/**
 * [BusinessFlag] is the default type of [Flag]. It must be accessed WITH a business guid.
 */
sealed class BusinessFlag<T>(
  override val featureKey: String,
  override val defaultValue: T,
) : Flag<T> {
  object BusinessApprovedFlowBypass : BusinessFlag<Boolean>(
    featureKey = "business-approved-flow-bypass",
    defaultValue = false,
  )

  object DisableCheckDepositFailedEmails : BusinessFlag<Boolean>(
    featureKey = "disable-check-deposit-failed-emails",
    defaultValue = false,
  )

  object CapitalSpvFlowOfFunds : BusinessFlag<Boolean>(
    featureKey = "capital-spv-flow-of-funds",
    defaultValue = false,
  )

  object CapitalTaktileKyb : BusinessFlag<Boolean>(
    featureKey = "capital-taktile-kyb",
    defaultValue = false,
  )

  object CreateInternationalAccountOnApproval : BusinessFlag<Boolean>(
    featureKey = "create-international-account-on-approval",
    defaultValue = false,
  )

  object CreateInternationalAccountOnUpgrade : BusinessFlag<Boolean>(
    featureKey = "create-international-account-on-upgrade",
    defaultValue = false,
  )

  object CustomPaymentCreatedConfirmationEmails : BusinessFlag<String>(
    featureKey = "custom-payment-created-confirmation-email",
    defaultValue = "",
  )

  object FailedDebitsUi : BusinessFlag<Boolean>(
    featureKey = "failed-debits-ui",
    defaultValue = false,
  )

  object FedExCardShipping : BusinessFlag<Boolean>(
    featureKey = "fedex-card-shipping",
    defaultValue = false,
  )

  object ForceBlueRidgeQbo : BusinessFlag<Boolean>(
    featureKey = "force-blue-ridge-qbo",
    defaultValue = false,
  )

  object IncomingTransactionNotification : BusinessFlag<Boolean>(
    featureKey = "incoming-transaction-notification",
    defaultValue = false,
  )

  object IncomingDebitTransactionNotification : BusinessFlag<Boolean>(
    featureKey = "incoming-debit-transaction-notification",
    defaultValue = false,
  )

  object IncomingWireTransactionNotification : BusinessFlag<Boolean>(
    featureKey = "incoming-wire-transaction-notification",
    defaultValue = false,
  )

  object MultiBusiness : BusinessFlag<Boolean>(
    featureKey = "multi-business",
    defaultValue = false,
  )

  object PaymentCanceledEmail : BusinessFlag<Boolean>(
    featureKey = "payment-canceled-email",
    defaultValue = false,
  )

  object PreventDepositAccountCreation : BusinessFlag<Boolean>(
    featureKey = "prevent-deposit-account-creation",
    defaultValue = false,
  )

  object PreventDacaDepositAccountCreation : BusinessFlag<Boolean>(
    featureKey = "prevent-daca-deposit-account-creation",
    defaultValue = false,
  )

  object PaymentPolicies : BusinessFlag<Boolean>(
    featureKey = "payment-policies",
    defaultValue = false,
  )

  object SameDayAchDomesticAch : BusinessFlag<Boolean>(
    featureKey = "same-day-ach-domestic-ach",
    defaultValue = false,
  )

  object SendTransactionEmailToSender : BusinessFlag<Boolean>(
    featureKey = "send-transaction-email-to-sender",
    defaultValue = false,
  )

  object SendCheckPaymentReturnedEmail : BusinessFlag<Boolean>(
    featureKey = "send-check-payment-returned-email",
    defaultValue = false,
  )

  object StopAchDebitsForHighYieldAccounts : BusinessFlag<Boolean>(
    featureKey = "stop-all-ach-debits-for-high-yield-accounts",
    defaultValue = false,
  )

  object SkipFailedAchDebitEmail : BusinessFlag<Boolean>(
    featureKey = "skip-failed-ach-debit-email",
    defaultValue = false,
  )

  object SwiftCollectionsAutomatic : BusinessFlag<Boolean>(
    featureKey = "swift-collection-automatic",
    defaultValue = false,
  )

  object PaymentsThroughBackend : BusinessFlag<Boolean>(
    featureKey = "payments-through-backend",
    defaultValue = false,
  )

  object VgsiNewTerms : BusinessFlag<Boolean>(
    featureKey = "vgsi-new-terms",
    defaultValue = false,
  )

  object WriteTransactionSyncToGcs : BusinessFlag<Boolean>(
    featureKey = "write-transaction-sync-to-gcs",
    defaultValue = false,
  )
}
