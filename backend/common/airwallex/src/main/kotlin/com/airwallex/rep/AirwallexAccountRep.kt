package com.airwallex.rep

import com.fasterxml.jackson.databind.JsonNode
import java.time.ZonedDateTime

/**
 * Airwallex Account representation for connected account operations.
 * Used for platform scenarios where you manage multiple connected accounts.
 */
data class AirwallexAccountRep(
  val id: String,
  val accountDetails: JsonNode,
  val createdAt: ZonedDateTime,
  val customerAgreements: JsonNode,
  val metadata: JsonNode,
  val nextAction: JsonNode,
  val nickname: String,
  val primaryContact: JsonNode,
  val requirements: JsonNode,
  val status: String,
  val viewType: String,
) {
  data class Creator(
    val accountDetails: JsonNode?,
    val customerAgreements: JsonNode?,
    val metadata: JsonNode?,
    val nickname: String?,
    val primaryContact: JsonNode?,
  )

  data class Updater(
    val accountDetails: JsonNode?,
    val metadata: JsonNode?,
    val nickname: String?,
  )
}
