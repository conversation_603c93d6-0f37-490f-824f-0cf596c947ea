package com.airwallex.client

import com.airwallex.rep.AirwallexAccountRep
import com.airwallex.rep.PaginatedDataWrapper
import io.ktor.http.HttpMethod
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

/**
 * Airwallex Accounts Client for managing connected account operations.
 */
class AirwallexAccountsClient internal constructor(
  private val request: AirwallexRequest,
) {
  private val logger = KotlinLogging.logger {}

  /**
   * Get a specific account by ID.
   */
  suspend fun get(accountId: String): AirwallexAccountRep? {
    logger.debug { "Getting Airwallex account with ID: $accountId" }
    val response = request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/accounts/$accountId",
    )
    return response.readValue<AirwallexAccountRep?>()
  }

  /**
   * Create a new connected account.
   */
  suspend fun create(creator: AirwallexAccountRep.Creator): AirwallexAccountRep {
    logger.info { "Creating Airwallex account: accountName=${creator.nickname}" }

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/accounts/create",
      body = creator,
    )
    return response.readValue<AirwallexAccountRep>()
  }

  /**
   * Update an existing account.
   */
  suspend fun update(accountId: String, updater: AirwallexAccountRep.Updater): AirwallexAccountRep {
    logger.info { "Updating Airwallex account: $accountId" }

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/accounts/$accountId/update",
      body = updater,
    )
    return response.readValue<AirwallexAccountRep>()
  }

  /**
   * Submit an account for activation.
   */
  suspend fun submit(accountId: String): AirwallexAccountRep {
    logger.info { "Submitting Airwallex account for activation: $accountId" }

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/accounts/$accountId/submit",
    )
    return response.readValue<AirwallexAccountRep>()
  }

  /**
   * List all connected accounts for the platform.
   */
  fun search(
    accountStatus: String? = null,
    fromCreatedAt: ZonedDateTime? = null,
    toCreatedAt: ZonedDateTime? = null,
  ): Flow<AirwallexAccountRep> {
    logger.debug {
      "Listing all Airwallex accounts for platform with filters: status=$accountStatus, " +
        "fromCreatedAt=$fromCreatedAt, toCreatedAt=$toCreatedAt"
    }

    return request.paginatedRequest(
      request = { paginationParams ->
        val response = request.request(
          httpMethod = HttpMethod.Get,
          path = "/api/v1/accounts",
          qp = paginationParams + buildMap {
            accountStatus?.let { put("account_status", listOf(it.toString())) }
            fromCreatedAt?.let {
              put("from_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
            }
            toCreatedAt?.let {
              put("to_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
            }
          },
        )
        response.readValue<PaginatedDataWrapper<AirwallexAccountRep>>()
      },
      getResult = { wrapper -> wrapper.items },
    )
  }
}
