package com.airwallex.client

import co.highbeam.metrics.Metrics
import com.airwallex.rep.AirwallexTransferRep
import io.ktor.http.HttpMethod
import mu.KotlinLogging

class AirwallexTransferClient internal constructor(
  private val metrics: Metrics,
  private val request: AirwallexRequest,
) {
  private val logger = KotlinLogging.logger {}

  /**
   * Create a transfer from wallet.
   */
  suspend fun create(creator: AirwallexTransferRep.Creator): AirwallexTransferRep {
    logger.info { "Creating Airwallex transfer: ${creator.requestId}." }
    val sample = metrics.timeSample()

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/transfers/create",
      body = creator,
    )

    val transfer = response.readValue<AirwallexTransferRep>()

    val timer = metrics.timer(
      "airwallex.transfer.create",
      "currency",
      creator.sourceCurrency ?: "unknown",
      "status",
      transfer.status,
    )
    sample.stop(timer)

    metrics.counter(
      "transfer_volume",
      "event_type",
      "transfer",
      "currency",
      creator.sourceCurrency ?: "unknown",
    ) {}.increment(creator.sourceAmount?.toDouble() ?: 0.0)

    return transfer
  }

  /**
   * Get transfer.
   */
  suspend fun get(id: String): AirwallexTransferRep? {
    logger.debug { "Getting Airwallex payout $id." }
    val response = request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/transfers/$id",
    )
    return response.readValue<AirwallexTransferRep?>()
  }

  /**
   * Cancel a transfer.
   */
  suspend fun cancel(id: String): AirwallexTransferRep {
    logger.info { "Cancelling Airwallex payout: $id" }

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/transfers/$id/cancel",
    )
    return response.readValue<AirwallexTransferRep>()
  }
}
