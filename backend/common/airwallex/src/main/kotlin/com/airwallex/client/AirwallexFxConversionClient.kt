package com.airwallex.client

import co.highbeam.metrics.Metrics
import com.airwallex.rep.AirwallexFxConversionRep
import com.airwallex.rep.PaginatedDataWrapper
import io.ktor.http.HttpMethod
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

/**
 * Airwallex FX Conversion Client for managing foreign exchange conversion operations.
 */
class AirwallexFxConversionClient internal constructor(
  private val metrics: Metrics,
  private val request: AirwallexRequest,
) {
  private val logger = KotlinLogging.logger {}

  /**
   * Create a conversion (execute FX trade).
   */
  suspend fun create(creator: AirwallexFxConversionRep.Creator): AirwallexFxConversionRep {
    logger.info {
      "Creating FX conversion: ${creator.sellCurrency} to ${creator.buyCurrency}, " +
        "buyAmount=${creator.buyAmount}, sellAmount=${creator.sellAmount}, " +
        "quoteId=${creator.quoteId}"
    }
    val sample = metrics.timeSample()

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/fx/conversions/create",
      body = creator,
    )

    val conversion = response.readValue<AirwallexFxConversionRep>()

    val timer = metrics.timer(
      "airwallex.fx.conversion.create",
      "buyCurrency", creator.buyCurrency,
      "sellCurrency", creator.sellCurrency,
      "hasQuote", (creator.quoteId != null).toString(),
      "status", conversion.status,
    )
    sample.stop(timer)

    metrics.counter(
      "fx_conversion_volume",
      "event_type", "conversion_created",
      "buy_currency", creator.buyCurrency,
      "sell_currency", creator.sellCurrency,
      "has_quote", (creator.quoteId != null).toString(),
    ) {}.increment(
      creator.buyAmount?.toDouble() ?: creator.sellAmount?.toDouble() ?: 0.0
    )

    return conversion
  }

  /**
   * Get conversion by ID.
   */
  suspend fun get(conversionId: String): AirwallexFxConversionRep? {
    logger.debug { "Getting FX conversion with ID: $conversionId" }
    val response = request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/fx/conversions/$conversionId",
    )
    return response.readValue<AirwallexFxConversionRep?>()
  }

  /**
   * List conversions with filtering options.
   */
  fun list(
    buyCurrency: String? = null,
    sellCurrency: String? = null,
    status: String? = null,
    fromCreatedAt: ZonedDateTime? = null,
    toCreatedAt: ZonedDateTime? = null,
  ): Flow<AirwallexFxConversionRep> {
    logger.debug {
      "Listing FX conversions with filters: buyCurrency=$buyCurrency, " +
        "sellCurrency=$sellCurrency, status=$status, fromCreatedAt=$fromCreatedAt, " +
        "toCreatedAt=$toCreatedAt"
    }

    return request.paginatedRequest(
      request = { paginationParams ->
        val response = request.request(
          httpMethod = HttpMethod.Get,
          path = "/api/v1/fx/conversions",
          qp = paginationParams + buildMap {
            buyCurrency?.let { put("buy_currency", listOf(it)) }
            sellCurrency?.let { put("sell_currency", listOf(it)) }
            status?.let { put("status", listOf(it)) }
            fromCreatedAt?.let {
              put("from_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
            }
            toCreatedAt?.let {
              put("to_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
            }
          },
        )
        response.readValue<PaginatedDataWrapper<AirwallexFxConversionRep>>()
      },
      getResult = { wrapper -> wrapper.items },
    )
  }
}
