package com.airwallex.client

import co.highbeam.metrics.Metrics
import co.highbeam.money.Money
import com.airwallex.rep.FxQuoteRep
import com.airwallex.rep.FxRateRep
import io.ktor.http.HttpMethod
import mu.KotlinLogging
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * Airwallex FX Quote Client for managing foreign exchange quote operations.
 */
class AirwallexFxQuoteClient internal constructor(
  private val metrics: Metrics,
  private val request: AirwallexRequest,
) {
  private val logger = KotlinLogging.logger {}

  /**
   * Get the current FX rate for a currency pair.
   */
  suspend fun getRate(
    buyCurrency: String,
    sellCurrency: String,
    buyAmount: Money? = null,
    sellAmount: Money? = null,
    conversionDate: LocalDate? = null,
  ): FxRateRep {
    require((buyAmount != null) xor (sellAmount != null)) {
      "Either buyAmount or sellAmount must be specified, but not both"
    }

    logger.debug {
      "Getting current FX rate for $sellCurrency to $buyCurrency, " +
        "buyAmount=$buyAmount, sellAmount=$sellAmount"
    }
    val sample = metrics.timeSample()

    val response = request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/fx/rates/current",
      qp = buildMap {
        put("buy_currency", listOf(buyCurrency))
        put("sell_currency", listOf(sellCurrency))
        buyAmount?.let { put("buy_amount", listOf(it.toString())) }
        sellAmount?.let { put("sell_amount", listOf(it.toString())) }
        conversionDate?.let {
          put("conversion_date", listOf(it.format(DateTimeFormatter.ISO_LOCAL_DATE)))
        }
      },
    )

    val rate = response.readValue<FxRateRep>()

    val timer = metrics.timer(
      "airwallex.fx.rate.get",
      "buyCurrency",
      buyCurrency,
      "sellCurrency",
      sellCurrency,
    )
    sample.stop(timer)

    return rate
  }

  /**
   * Create a guaranteed FX quote.
   */
  suspend fun create(creator: FxQuoteRep.Creator): FxQuoteRep {
    logger.info {
      "Creating FX quote: ${creator.sellCurrency} to ${creator.buyCurrency}, " +
        "buyAmount=${creator.buyAmount}, sellAmount=${creator.sellAmount}, " +
        "validity=${creator.validity}"
    }
    val sample = metrics.timeSample()

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/fx/quotes/create",
      body = creator,
    )

    val quote = response.readValue<FxQuoteRep>()

    val timer = metrics.timer(
      "airwallex.fx.quote.create",
      "buyCurrency",
      creator.buyCurrency,
      "sellCurrency",
      creator.sellCurrency,
      "validity",
      creator.validity,
    )
    sample.stop(timer)

    metrics.counter(
      "fx_quote_volume",
      "event_type", "quote_created",
      "buy_currency", creator.buyCurrency,
      "sell_currency", creator.sellCurrency,
      "validity", creator.validity,
    ) {}.increment(
      creator.buyAmount?.toDouble() ?: creator.sellAmount?.toDouble() ?: 0.0
    )

    return quote
  }

  /**
   * Get quote by ID.
   */
  suspend fun get(quoteId: String): FxQuoteRep? {
    logger.debug { "Getting FX quote with ID: $quoteId" }
    val response = request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/fx/quotes/$quoteId",
    )
    return response.readValue<FxQuoteRep?>()
  }
}
