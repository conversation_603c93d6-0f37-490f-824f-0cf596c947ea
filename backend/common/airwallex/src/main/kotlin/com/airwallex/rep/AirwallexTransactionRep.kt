package com.airwallex.rep

import java.math.BigDecimal

/**
 * Airwallex Transaction representation for transaction operations.
 */
data class AirwallexTransactionRep(
  val amount: BigDecimal,
  val createTime: String,
  val currency: String,
  val description: String,
  val feeAmount: BigDecimal,
  val feeCurrency: String,
  val id: String,
  val payerCountry: String,
  val payerName: String,
  val status: String,
  val type: String,
)
