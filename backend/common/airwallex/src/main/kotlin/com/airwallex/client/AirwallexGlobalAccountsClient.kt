package com.airwallex.client

import com.airwallex.rep.AirwallexGlobalAccountRep
import com.airwallex.rep.AirwallexTransactionRep
import com.airwallex.rep.PaginatedDataWrapper
import io.ktor.http.HttpMethod
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

/**
 * Airwallex Global Accounts Client for managing global account operations.
 */
class AirwallexGlobalAccountsClient internal constructor(
  private val request: AirwallexRequest,
) {
  private val logger = KotlinLogging.logger {}

  /**
   * List global accounts with filtering options.
   */
  suspend fun list(
    currencyCode: String?,
    fromCreatedAt: ZonedDateTime? = null,
    toCreatedAt: ZonedDateTime? = null,
    requiredFeaturesCurrency: String? = null,
    supportedFeaturesCurrency: String? = null,
    status: String? = null,
  ): List<AirwallexGlobalAccountRep> {
    logger.debug {
      "Listing Airwallex global accounts with filters: fromCreatedAt=$fromCreatedAt, " +
        "toCreatedAt=$toCreatedAt, requiredFeaturesCurrency=$requiredFeaturesCurrency, " +
        "supportedFeaturesCurrency=$supportedFeaturesCurrency, status=$status"
    }

    return request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/global_accounts",
      qp = buildMap {
        currencyCode?.let { put("currency_code", listOf(it)) }
        fromCreatedAt?.let {
          put("from_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
        }
        toCreatedAt?.let {
          put("to_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
        }
        requiredFeaturesCurrency?.let { put("required_features.currency", listOf(it)) }
        supportedFeaturesCurrency?.let { put("supported_features.currency", listOf(it)) }
        status?.let { put("status", listOf(it)) }
      },
    ).readValue<PaginatedDataWrapper<AirwallexGlobalAccountRep>>().items
  }

  /**
   * Get a specific global account by ID.
   */
  suspend fun get(globalAccountId: String): AirwallexGlobalAccountRep? {
    logger.debug { "Getting Airwallex global account with ID: $globalAccountId" }
    val response = request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/global_accounts/$globalAccountId",
    )
    return response.readValue<AirwallexGlobalAccountRep?>()
  }

  /**
   * Create a new global account.
   */
  suspend fun create(creator: AirwallexGlobalAccountRep.Creator): AirwallexGlobalAccountRep {
    logger.info {
      "Creating Airwallex global account: countryCode=${creator.countryCode}, " +
        "nickName=${creator.nickName}, requiredFeatures=${creator.requiredFeatures.size} features"
    }

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/global_accounts/create",
      body = creator,
    )
    return response.readValue<AirwallexGlobalAccountRep>()
  }

  /**
   * Update a global account.
   */
  suspend fun update(
    globalAccountId: String,
    updater: AirwallexGlobalAccountRep.Updater
  ): AirwallexGlobalAccountRep {
    logger.info {
      "Updating Airwallex global account: $globalAccountId, nickName=${updater.nickName}, " +
        "alternateAccountIdentifiers=${updater.alternateAccountIdentifiers}"
    }

    val response = request.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/global_accounts/$globalAccountId/update",
      body = updater,
    )
    return response.readValue<AirwallexGlobalAccountRep>()
  }

  /**
   * Get transactions for a specific global account.
   */
  fun getTransactions(
    globalAccountId: String,
    fromCreatedAt: ZonedDateTime? = null,
    toCreatedAt: ZonedDateTime? = null,
  ): Flow<AirwallexTransactionRep> {
    logger.debug {
      "Getting transactions for Airwallex global account: $globalAccountId, " +
        "fromCreatedAt=$fromCreatedAt, toCreatedAt=$toCreatedAt"
    }

    return request.paginatedRequest(
      request = { paginationParams ->
        val response = request.request(
          httpMethod = HttpMethod.Get,
          path = "/api/v1/global_accounts/$globalAccountId/transactions",
          qp = paginationParams + buildMap {
            fromCreatedAt?.let {
              put("from_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
            }
            toCreatedAt?.let {
              put("to_created_at", listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)))
            }
          },
        )
        response.readValue<PaginatedDataWrapper<AirwallexTransactionRep>>()
      },
      getResult = { wrapper -> wrapper.items },
    )
  }
}
