package com.airwallex.rep

import com.fasterxml.jackson.databind.JsonNode
import java.math.BigDecimal

/**
 * Airwallex Transfer representation for bank transfers.
 */
data class AirwallexTransferRep(
  val amountBeneficiaryReceives: BigDecimal,
  val amountPayerPays: BigDecimal,
  val applicableFeeOptions: List<ApplicationFeeOption>,
  val batchTransferId: String,
  val beneficiary: JsonNode,
  val beneficiaryId: String,
  val createdAt: String,
  val dispatchDate: String,
  val dispatchInfo: JsonNode,
  val failureReason: String,
  val failureType: String,
  val feeAmount: BigDecimal,
  val feeCurrency: String,
  val feePaidBy: String,
  val funding: JsonNode,
  val id: String,
  val metadata: JsonNode,
  val payer: JsonNode,
  val payerId: String,
  val reason: String,
  val reference: String,
  val remarks: String,
  val requestId: String,
  val shortReferenceId: String,
  val sourceAmount: BigDecimal,
  val sourceCurrency: String,
  val status: String,
  val swiftChargeOption: String,
  val transferAmount: BigDecimal,
  val transferCurrency: String,
  val transferDate: String,
  val transferMethod: String,
  val updatedAt: String,
  val conversionId: String,
) {
  /**
   * Application fee option for transfers.
   * Represents fees charged by the platform for transfer services.
   */
  data class ApplicationFeeOption(
    val sourceType: String,
    val type: String,
    val amount: Long,
    val currency: String,
  )

  data class Creator(
    val applicableFeeOptions: List<ApplicationFeeOption>?,
    val beneficiary: JsonNode?,
    val beneficiaryId: String?,
    val clientData: String?,
    val feePaidBy: String?,
    val metadata: JsonNode?,
    val payer: JsonNode?,
    val payerId: String?,
    val quoteId: String?,
    val reason: String,
    val reference: String,
    val remarks: String?,
    val requestId: String,
    val sourceAmount: BigDecimal?,
    val sourceCurrency: String?,
    val swiftChargeOption: String?,
    val transferAmount: BigDecimal?,
    val transferCurrency: String?,
    val transferDate: String?,
    val transferMethod: String?,
  )
}
