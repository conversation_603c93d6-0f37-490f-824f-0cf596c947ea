package com.airwallex.rep

import com.fasterxml.jackson.databind.JsonNode

/**
 * Airwallex Global Account representation for global account operations.
 */
data class AirwallexGlobalAccountRep(
  val accountName: String,
  val accountNumber: String,
  val accountType: String,
  val alternateAccountIdentifiers: JsonNode,
  val closeReason: String,
  val countryCode: String,
  val depositConversionCurrency: String,
  val failureReason: String,
  val iban: String,
  val id: String,
  val institution: JsonNode,
  val nickName: String,
  val requestId: String,
  val requiredFeatures: List<JsonNode>,
  val status: String,
  val supportFeatures: List<JsonNode>,
  val swiftCode: String,
) {
  data class Creator(
    val alternateAccountIdentifiers: JsonNode?,
    val countryCode: String,
    val depositConversionCurrency: String?,
    val nickName: String?,
    val requestId: String,
    val requiredFeatures: List<JsonNode>,
  )

  data class Updater(
    val alternateAccountIdentifiers: JsonNode?,
    val depositConversionCurrency: String?,
    val nickName: String,
  )
}
