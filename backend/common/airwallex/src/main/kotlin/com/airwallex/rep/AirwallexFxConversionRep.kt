package com.airwallex.rep

import com.fasterxml.jackson.databind.JsonNode
import java.math.BigDecimal

data class AirwallexFxConversionRep(
  val applicationFeeOptions: JsonNode?,
  val applicationFees: List<JsonNode>,
  val awxRate: BigDecimal,
  val buyAmount: BigDecimal,
  val buyCurrency: String,
  val clientRate: BigDecimal,
  val conversionDate: String,
  val conversionId: String,
  val createdAt: String,
  val currencyPair: String,
  val funding: JsonNode,
  val fundingSource: JsonNode,
  val midRate: BigDecimal,
  val quoteId: String,
  val rateDetails: List<JsonNode>,
  val requestId: String,
  val sellAmount: BigDecimal,
  val sellCurrency: String,
  val settlementCutoffAt: String,
  val shortReferenceId: String,
  val status: String,
  val updatedAt: String,
) {
  data class Creator(
    val applicationFeeOptions: JsonNode?,
    val buyAmount: BigDecimal?,
    val buyCurrency: String,
    val conversionDate: String?,
    val fundingSource: JsonNode?,
    val metadata: JsonNode?,
    val quoteId: String?,
    val requestId: String,
    val sellAmount: BigDecimal?,
    val sellCurrency: String,
  )
}
