package com.airwallex.client

import co.highbeam.client.HttpResponse
import co.highbeam.protectedString.ProtectedString
import com.airwallex.rep.PaginatedDataWrapper
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import mu.KotlinLogging
import org.assertj.core.util.VisibleForTesting
import java.time.Clock
import java.time.ZonedDateTime

internal class AirwallexRequest(
  private val httpClient: AirwallexHttpClient,
  private val apiKey: ProtectedString,
  private val clientId: String,
  private val clock: Clock,
  private val onBehalfOf: String? = null,
) {
  private val logger = KotlinLogging.logger {}

  private var cachedToken: String? = null
  private var tokenExpiresAt: ZonedDateTime? = null

  suspend fun request(
    httpMethod: HttpMethod,
    path: String,
    qp: Map<String, List<String>> = emptyMap(),
    body: Any? = null,
  ): HttpResponse {
    val token = getValidAuthToken()
    return httpClient.request(
      httpMethod = httpMethod,
      path = path,
      qp = qp,
      body = body,
      builder = {
        putHeader(HttpHeaders.Authorization, "Bearer $token")
        onBehalfOf?.let { putHeader("x-on-behalf-of", it) }
      },
    )
  }

  private suspend fun getValidAuthToken(): String {
    val now = ZonedDateTime.now(clock)

    // Check if we have a valid cached token
    cachedToken?.let { token ->
      tokenExpiresAt?.let { expiresAt ->
        if (now.isBefore(expiresAt.minusMinutes(5))) { // Refresh 5 minutes before expiry
          return token
        }
      }
    }

    return refreshAuthToken()
  }

  private suspend fun refreshAuthToken(): String {
    logger.info { "Refreshing Airwallex authentication token." }

    val response = httpClient.request(
      httpMethod = HttpMethod.Post,
      path = "/api/v1/authentication/login",
      body = emptyMap<String, String>(), // Empty body for POST request
      builder = {
        putHeader("x-client-id", clientId)
        putHeader("x-api-key", apiKey.value)
        putHeader("Content-Type", "application/json")
      }
    )

    val authResponse = response.readValue<AuthResponse>()

    cachedToken = authResponse.token
    tokenExpiresAt = authResponse.expiresAtDateTime

    logger.info {
      "Successfully refreshed Airwallex authentication token, " +
        "expires at ${authResponse.expiresAtDateTime}."
    }

    return authResponse.token
  }

  private data class AuthResponse(
    val token: String,
    val expiresAt: String, // ISO 8601 format
  ) {
    val expiresAtDateTime: ZonedDateTime
      get() = ZonedDateTime.parse(expiresAt)
  }

  @VisibleForTesting
  fun <T : Any> paginatedRequest(
    request: suspend (paginationParams: Map<String, List<String>>) -> PaginatedDataWrapper<T>,
    getResult: (wrapper: PaginatedDataWrapper<T>) -> List<T>,
  ): Flow<T> {
    val limit = 100 // Airwallex typical page size
    return flow {
      var offset = 0
      while (true) {
        val paginationParams = mapOf(
          "page_size" to listOf(limit.toString()),
          "page_num" to listOf((offset / limit + 1).toString()),
        )
        val paginatedDataWrapper = request(paginationParams)
        val results = getResult(paginatedDataWrapper)
        results.forEach { emit(it) }
        if (paginatedDataWrapper.hasMore == null || !paginatedDataWrapper.hasMore) break
        offset += limit
      }
    }
  }
}
