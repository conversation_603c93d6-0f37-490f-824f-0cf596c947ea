package com.airwallex.client

import com.airwallex.rep.AirwallexBalanceRep
import io.ktor.http.HttpMethod
import mu.KotlinLogging

class AirwallexBalanceClient internal constructor(
  private val request: AirwallexRequest,
) {
  private val logger = KotlinLogging.logger {}

  /**
   * Get current wallet balances for all currencies.
   */
  suspend fun get(): List<AirwallexBalanceRep> {
    logger.debug { "Getting current Airwallex wallet balances" }
    val response = request.request(
      httpMethod = HttpMethod.Get,
      path = "/api/v1/balances/current",
    )
    return response.readValue<List<AirwallexBalanceRep>>()
  }
}
