package com.airwallex.client

import co.highbeam.metrics.Metrics
import co.highbeam.protectedString.ProtectedString
import java.time.Clock

class AirwallexClient(
  private val httpClient: AirwallexHttpClient,
  private val metrics: Metrics,
  private val apiKey: ProtectedString,
  private val clientId: String,
  private val clock: Clock,
  private val onBehalfOf: String? = null,
) {
  private val request: AirwallexRequest =
    AirwallexRequest(httpClient, apiKey, clientId, clock, onBehalfOf)

  val accounts: AirwallexAccountsClient = AirwallexAccountsClient(request)
  val balance: AirwallexBalanceClient = AirwallexBalanceClient(request)
  val fxConversion: AirwallexFxConversionClient = AirwallexFxConversionClient(metrics, request)
  val fxQuote: AirwallexFxQuoteClient = AirwallexFxQuoteClient(metrics, request)
  val globalAccounts: AirwallexGlobalAccountsClient = AirwallexGlobalAccountsClient(request)
  val transfer: AirwallexTransferClient = AirwallexTransferClient(metrics, request)

  /**
   * Execute a block of operations on behalf of a connected account.
   */
  suspend fun <T> onBehalfOf(accountId: String, block: suspend AirwallexClient.() -> T): T {
    val connectedClient = AirwallexClient(
      httpClient = httpClient,
      metrics = metrics,
      apiKey = apiKey,
      clientId = clientId,
      clock = clock,
      onBehalfOf = accountId,
    )
    return block(connectedClient)
  }
}
