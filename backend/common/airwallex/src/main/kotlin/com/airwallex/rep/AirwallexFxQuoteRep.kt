package com.airwallex.rep

import com.fasterxml.jackson.databind.JsonNode
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime

/**
 * Airwallex FX Rate representation for current market rates.
 */
data class FxRateRep(
  val buyCurrency: String,
  val conversionDate: LocalDate,
  val createdAt: ZonedDateTime,
  val currencyPair: String,
  val dealtCurrency: String,
  val rate: BigDecimal,
  val rateDetails: List<JsonNode>,
  val sellCurrency: String,
)

/**
 * Airwallex FX Quote representation for guaranteed quotes.
 */
data class FxQuoteRep(
  val applicationFees: List<JsonNode>,
  val awxRate: BigDecimal,
  val buyAmount: BigDecimal,
  val buyCurrency: String,
  val clientRate: BigDecimal,
  val conversionDate: LocalDate,
  val currencyPair: String,
  val dealtCurrency: String,
  val midRate: BigDecimal,
  val quoteId: String,
  val rateDetails: List<JsonNode>,
  val sellAmount: BigDecimal,
  val sellCurrency: String,
  val usage: String,
  val validFromAt: ZonedDateTime,
  val validToAt: ZonedDateTime,
  val validity: String,
) {
  data class Creator(
    val applicationFees: List<JsonNode>?,
    val buyAmount: BigDecimal?,
    val buyCurrency: String,
    val conversionDate: LocalDate?,
    val sellAmount: BigDecimal?,
    val sellCurrency: String,
    val validity: String,
  )
}
