package co.highbeam.service.paymentV2.converter

import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.auth.getRestContext
import co.highbeam.auth.principal.JwtAccess
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import mu.KotlinLogging

internal class PaymentConverter @Inject constructor(
  private val businessMemberClient: BusinessMemberClient,
  private val strategyFactory: PaymentConverterStrategy.Factory,
  private val uuidGenerator: UuidGenerator,
) {
  private val logger = KotlinLogging.logger {}

  @OptIn(JwtAccess::class)
  suspend fun fromCreator(creator: PaymentRep.Creator): Pair<PaymentRep, SendPaymentCreatorRep?> {
    logger.info { "Converting payment: $creator." }

    val strategy = strategyFactory.forType(creator.detail)
    logger.info { "Using strategy: $strategy." }

    var (payment, sender) = strategy.fromCreator(
      creator = creator,
      guid = uuidGenerator.generate(),
      status = if (creator.send) PaymentRep.Status.Pending else PaymentRep.Status.Open,
      createdByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
    )

    // If the caller supplied a business member GUID for the user initiating the payment, use that
    // to populate the createdByUserGuid field on the Payment. Otherwise, createdByUserGuid defaults
    // to the user guid based on the JWT (if there is one). Note: initiatorBusinessMemberGuid should
    // have already been validated up in the auth layer.
    // This is useful for cases where the "create payment" call is proxied through another service,
    // and we want to preserve the original user who initiated the payment. For example, this is
    // currently a use case for the Pay Bill API (in backend V2 AP feature), which is called from
    // the frontend and, in turn, calls the payment gateway to create a payment.
    creator.initiatorBusinessMemberGuid?.let { initiatorBusinessMemberGuid ->
      val businessMember = businessMemberClient.request(
        BusinessMemberApi.Get(
          businessGuid = payment.businessGuid,
          memberGuid = initiatorBusinessMemberGuid,
        ),
      )
      businessMember?.let { businessMember ->
        payment = payment.copy(
          createdByUserGuid = businessMember.userGuid,
        )
      }
    }

    return Pair(payment, sender)
  }
}
