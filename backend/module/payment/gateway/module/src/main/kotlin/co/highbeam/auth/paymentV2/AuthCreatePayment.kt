package co.highbeam.auth.paymentV2

import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthUser
import co.highbeam.auth.jwt.Jwt
import co.highbeam.auth.permissions.Permission
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import com.google.inject.Inject
import io.ktor.http.Headers
import mu.KotlinLogging
import java.time.Duration

private val IMMEDIATE_MFA_DURATION = Duration.ofSeconds(150)

internal class AuthCreatePayment private constructor(
  private val authMfa: AuthMfa,
  private val authPermission: AuthPermission,
  private val authUser: AuthUser?,
) : Auth() {
  internal class Provider @Inject constructor(
    private val authMfa: AuthMfa.Provider,
    private val authPermission: AuthPermission.Provider,
    private val authUser: AuthUser.Provider,
    private val featureFlagService: FeatureFlagService,
  ) {
    private val logger = KotlinLogging.logger {}

    operator fun invoke(
      creator: PaymentRep,
    ): Auth =
      AuthCreatePayment(
        authMfa = run authMfa@{
          val requiresImmediateMfa = creator.detail.requiresImmediateMfa()
          logger.debug { "authMfa requiresImmediateMfa is $requiresImmediateMfa." }
          return@authMfa if (requiresImmediateMfa) authMfa(IMMEDIATE_MFA_DURATION) else authMfa()
        },
        authPermission = run authPermission@{
          val businessGuid = creator.businessGuid

          val permission = run permission@{
            // Temporary shim:
            // If payment policies are enabled for this business, then the user only needs to be
            // able to draft payments. Actual sending of payments is delegated to the payment
            // policy system, and all payments (regardless of the permissions of the user who
            // created them) are subject to the same potential enforcements as determined by the
            // payment policy.
            // NB(lev): Permission.PaymentApproval_Create is being used here for now because this is
            // the existing permission (typically assigned to bookkeepers, by default) that controls
            // whether a payment can be drafted. Moving forward, we may consider migrating this to a
            // new permission which more accurately expresses the concept: "permission to submit a
            // payment".
            val isPaymentPoliciesEnabled = featureFlagService.isEnabled(
              flag = BusinessFlag.PaymentPolicies,
              businessGuid = businessGuid,
            )
            if (isPaymentPoliciesEnabled) {
              return@permission Permission.PaymentApproval_Create
            }

            return@permission when (val status = creator.status) {
              PaymentRep.Status.Open -> Permission.PaymentApproval_Create
              PaymentRep.Status.Pending -> permissionWhenPending(creator)
              else -> error("Unsupported payment status for auth: $status.")
            }
          }
          logger.debug { "authPermission permission is $permission." }
          return@authPermission authPermission(permission) { businessGuid }
        },
        authUser = run authUser@{
          val userGuid = creator.createdByUserGuid ?: return@authUser null
          return@authUser authUser(userGuid)
        }
      )

    private fun permissionWhenPending(creator: PaymentRep): Permission {
      return when (creator.detail) {
        is PaymentDetailRep.Ach -> Permission.Payment_CreateAny
        is PaymentDetailRep.DomesticWire -> Permission.Payment_CreateAny
        is PaymentDetailRep.InternationalWire -> Permission.Payment_Create
        is PaymentDetailRep.UnitPayment -> Permission.Payment_Create
        is PaymentDetailRep.UnitTransfer -> Permission.Payment_CreateTransfer
      }
    }
  }

  override suspend fun authorizeJwt(jwt: Jwt?, headers: Headers): Boolean {
    val auths = listOfNotNull(authMfa, authPermission, authUser)
    return All(auths).authorize(jwt, headers)
  }
}
