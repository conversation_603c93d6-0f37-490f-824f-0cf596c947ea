package co.highbeam.client.airwallexBankAccount

import co.highbeam.api.airwallexBankAccount.AirwallexBankAccountApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.bankAccounts.BANK_ACCOUNTS_FEATURE
import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep
import com.google.inject.Inject
import com.google.inject.name.Named

class AirwallexBankAccountClient @Inject constructor(
  @Named(BANK_ACCOUNTS_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: AirwallexBankAccountApi.Create): AirwallexBankAccountRep =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: AirwallexBankAccountApi.Get): AirwallexBankAccountRep? =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: AirwallexBankAccountApi.GetByBusinessGuid,
  ): AirwallexBankAccountRep? = httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: AirwallexBankAccountApi.GetByAirwallexAccountId,
  ): AirwallexBankAccountRep? = httpClient.request(endpoint).readValue()
}
