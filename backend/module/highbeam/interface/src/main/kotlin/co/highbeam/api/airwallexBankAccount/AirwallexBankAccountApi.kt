package co.highbeam.api.airwallexBankAccount

import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object AirwallexBankAccountApi {
  data class Create(val rep: AirwallexBankAccountRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/airwallex-bank-accounts",
    body = rep,
  )

  data class Get(val guid: UUID) : Endpoint(
    path = "/airwallex-bank-accounts/$guid"
  )

  data class GetByBusinessGuid(val businessGuid: UUID) : Endpoint(
    path = "/airwallex-bank-accounts",
    qp = mapOf("businessGuid" to listOf(businessGuid.toString())),
  )

  data class GetByAirwallexAccountId(val airwallexAccountId: String) : Endpoint(
    path = "/airwallex-bank-accounts",
    qp = mapOf("airwallexAccountId" to listOf(airwallexAccountId)),
  )
}
