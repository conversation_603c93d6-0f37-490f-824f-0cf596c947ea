package co.highbeam.rep.airwallexBankAccount

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import java.util.UUID

data class AirwallexBankAccountRep(
  val guid: UUID,
  val businessGuid: UUID,
  val airwallexAccountId: String,
  val airwallexWalletId: String?,
  val achRoutingNumber: String,
  val achAccountNumber: String,
  val wireRoutingNumber: String,
  val wireAccountNumber: String,
  val iban: String?,
  val swiftCode: String?,
  val enabled: Boolean,
) : CompleteRep {
  data class Creator(
    val businessGuid: UUID,
    val airwallexAccountId: String,
    val airwallexWalletId: String?,
    val achRoutingNumber: String,
    val achAccountNumber: String,
    val wireRoutingNumber: String,
    val wireAccountNumber: String,
    val iban: String?,
    val swiftCode: String?,
    val enabled: Boolean,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }
}
