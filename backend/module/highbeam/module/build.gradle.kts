plugins {
  id("highbeam-jvm")
}

kotlinJvmCompile {
  kotlinOptions.freeCompilerArgs += "-opt-in=io.ktor.util.InternalAPI"
}

dependencies {
  api(project(":common:auth0"))
  implementation(project(":common:airwallex"))
  implementation(project(":common:backend-v2"))
  implementation(project(":common:email"))
  implementation(project(":common:event"))
  implementation(project(":common:google-cloud-storage"))
  implementation(project(":common:hashing"))
  implementation(project(":common:job-runner"))
  api(project(":common:rest-feature"))
  implementation(project(":common:sql"))
  implementation(project(":common:task"))
  implementation(project(":common:unit-co"))
  implementation(project(":module:business:business:client"))
  implementation(project(":module:connect:client"))
  implementation(project(":module:job:client"))
  api(project(":module:highbeam:interface"))
  implementation(project(":module:capital:charge-card:client"))
  implementation(project(":module:capital:credit:client"))
  implementation(project(":module:capital:account:client"))
  implementation(project(":module:onboarding:client"))
  implementation(project(":module:transfer:client"))
  implementation(project(":module:transfer:client"))
  implementation(project(":module:user:client"))
  implementation(Dependencies.Apache.commonsCsv)
  implementation(Dependencies.Gcp.pubSub) // TODO: Move this to a common: library.
  implementation(Dependencies.Gcp.storage)
  api(Dependencies.Google.guava)
  implementation(Dependencies.Jwt.auth0JavaJwt)
  implementation(Dependencies.Jwt.auth0JwksRsa)
  implementation(Dependencies.Nimbusds.joseJwt)
  api(Dependencies.Plaid.plaid)
  implementation(project(":module:business:business:module"))
  implementation(project(":common:slack"))

  testImplementation(project(":module:capital:account:testing"))
  testImplementation(project(":module:capital:transaction:testing"))
  testImplementation(project(":module:capital:repayment:testing"))
  testImplementation(project(":common:event:testing"))
  testImplementation(project(":common:feature:feature-flags-testing"))
  testImplementation(project(":common:integration-testing"))
  testImplementation(project(":common:job-runner:testing"))
  testImplementation(project(":common:rest-feature:testing"))
  testImplementation(project(":common:sql:testing"))
  testImplementation(project(":common:task:testing"))
  testImplementation(project(":db:highbeam"))
  testImplementation(project(":module:highbeam:client"))
  testImplementation(Dependencies.Testing.junitParams)
}
