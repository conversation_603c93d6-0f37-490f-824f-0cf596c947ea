airwallex:
  environment: Sandbox
  apiKey:
    type: Plaintext
    value: test-airwallex-api-key
  clientId: test-airwallex-client-id
  slackWebhookPath: testAirwallexSlackWebhookPath

bankingConfig:
  slackWebhooks:
    monitoring: someSlackWebhookPath
  promotionAprilReferralGuids:
    - e2fd1034-abcb-40ed-8116-752c1880a470

currencyCloud:
  environment: Demo
  loginId: <EMAIL>
  apiKey:
    type: Plaintext
    value: 4411b30708dbeb0d200d1da4086e30979427f7bb9ffa98697585d1689052df56
  webhookKey:
    type: Plaintext
    value: test-currency-cloud-webhook-key
  slackWebhookPath: testSlackWebhookPath
  paymentFeeId: 6db5717e-2f9c-4775-ab4d-d3651d14820d

email:
  enabled: false

highbeamDatabase:
  jdbcUrl:
    type: Plaintext
    value: *****************************************
  username:
    type: EnvironmentVariable
    name: HIG<PERSON><PERSON>AM_TEST_POSTGRES_USERNAME
    defaultValue: highbeam
  password:
    type: EnvironmentVariable
    name: HIGHBEAM_TEST_POSTGRES_PASSWORD
  runMigrations: true
  connectionTimeout: 1000
  maximumPoolSize: 2

plaid:
  environment: Development
  webhookUrl: http://localhost:3000/plaid/webhook
  clientId: 6244724ca1f92500132352b6
  secret:
    type: Plaintext
    value: 3508b1b49b3d116af2dbd7599fae51

task:
  enabled: false
  projectName: fake
  locationName: fake
  cloudSchedulerRequestEmail: fake
  cloudSchedulerRequestAudience: fake
  cloudTasksToken:
    type: Plaintext
    value: test
