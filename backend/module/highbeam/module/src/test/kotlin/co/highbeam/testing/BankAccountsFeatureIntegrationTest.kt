package co.highbeam.testing

import co.highbeam.api.bankAccount.BankAccountSettingApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.FakeCapitalAccountClient
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.bankAccountSetting.BankAccountSettingClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.business.BusinessGuidClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.internationalBankAccount.InternationalBankAccountClient
import co.highbeam.client.job.JobClient
import co.highbeam.client.paymentDetails.PaymentDetailsClient
import co.highbeam.client.plaid.PlaidBankAccountSyncTaskClient
import co.highbeam.client.plaid.PlaidClient
import co.highbeam.client.rutter.RutterConnectionClient
import co.highbeam.client.transaction.InvoiceClient
import co.highbeam.client.transaction.UnitCoTransactionClient
import co.highbeam.client.user.UserClient
import co.highbeam.config.BankAccountsFeatureTestConfig
import co.highbeam.config.ConfigLoader
import co.highbeam.event.FakeEventFeature
import co.highbeam.feature.bankAccounts.BankAccountsFeature
import co.highbeam.feature.email.EmailFeature
import co.highbeam.feature.googleCloudStorage.GoogleCloudStorage
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.model.plaid.PlaidBankAccountModel
import co.highbeam.model.plaid.PlaidConnectionModel
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.bankAccountSetting.BankAccountSettingRep
import co.highbeam.server.Server
import co.highbeam.store.plaid.PlaidBankAccountStore
import co.highbeam.store.plaid.PlaidConnectionStore
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import com.currencycloud.client.CurrencyCloudClient
import com.plaid.client.model.AccountSubtype
import com.plaid.client.model.AccountType
import com.slack.client.SlackMessageClient
import highbeam.feature.task.TestTaskFeature
import highbeam.task.FakeTaskCreator
import highbeam.task.TaskCreator
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import java.util.UUID
import com.plaid.client.request.PlaidApi as InternalPlaidApi

@ExtendWith(BankAccountsFeatureIntegrationTest.Extension::class)
internal abstract class BankAccountsFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  private class MockFeature(
    val fakeFeatureFlagService: FakeFeatureFlagService,
    val fakeCapitalAccountClient: FakeCapitalAccountClient,
  ) : AbstractMockFeature() {
    override fun bind() {
      mock(BusinessClient::class)
      mock(BusinessGuidClient::class)
      mock(BusinessMemberClient::class)
      mock(CapitalAccountClient::class, fakeCapitalAccountClient)
      mock(CurrencyCloudClient::class)
      mock(FeatureFlagService::class, fakeFeatureFlagService)
      mock(GoogleCloudStorage::class)
      mock(InternalPlaidApi::class)
      mock(JobClient::class, mockk(relaxed = true))
      mock(PaymentDetailsClient::class)
      mock(RutterConnectionClient::class)
      mock(SlackMessageClient::class, mockk(relaxUnitFun = true))
      mock(UnitCoClient::class)
      mock(UserClient::class)
    }
  }

  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val config: BankAccountsFeatureTestConfig = ConfigLoader.load("bankAccounts/test")
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "bank_accounts",
      )
      val fakeFeatureFlagService = FakeFeatureFlagService()
      val fakeCapitalAccountClient = FakeCapitalAccountClient()
      val eventFeature = FakeEventFeature
      val taskFeature = TestTaskFeature(config.task)
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<BankAccountsFeatureTestConfig>(config) {
          override val features = setOf(
            EmailFeature(config.email),
            TestRestFeature(),
            sqlFeature,
            eventFeature,
            MockFeature(fakeFeatureFlagService, fakeCapitalAccountClient),
            BankAccountsFeature(
              config.bankingConfig,
              config.currencyCloud,
              config.airwallex,
              config.plaid
            ),
            taskFeature,
          )
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      sqlFeature.truncateSchema(context[Server::class.java].injector)
      fakeFeatureFlagService.reset()
      fakeCapitalAccountClient.reset()
      eventFeature.reset()
    }

    override fun stop() {
      sharedState.stop()
    }
  }

  val bankAccountClient: BankAccountClient by lazy {
    BankAccountClient(httpClient)
  }

  val bankAccountSettingClient: BankAccountSettingClient by lazy {
    BankAccountSettingClient(httpClient)
  }

  val internationalBankAccountClient: InternationalBankAccountClient by lazy {
    InternationalBankAccountClient(httpClient)
  }

  val invoiceClient: InvoiceClient by lazy {
    InvoiceClient(httpClient)
  }

  val plaidClient: PlaidClient by lazy {
    PlaidClient(httpClient)
  }

  val unitCoTransactionClient: UnitCoTransactionClient by lazy {
    UnitCoTransactionClient(httpClient)
  }

  val paymentDetailsClient: PaymentDetailsClient by lazy {
    PaymentDetailsClient(httpClient)
  }

  val plaidBankAccountSyncTaskClient: PlaidBankAccountSyncTaskClient by lazy {
    PlaidBankAccountSyncTaskClient(httpClient)
  }

  val userClient: UserClient by lazy {
    UserClient(httpClient)
  }

  val fakeTaskCreator: FakeTaskCreator by lazy {
    get<TaskCreator>() as FakeTaskCreator
  }

  protected fun createPlaidConnection(
    guid: UUID,
    businessGuid: UUID,
    plaidItemId: String = "plaid-item-id",
    accessToken: String = "plaid-access-token",
    isActive: Boolean = true,
    institutionId: String = "sample_institution_id",
    institutionName: String = "Test Institution",
  ): PlaidConnectionModel {
    return get<PlaidConnectionStore>().upsert(
      PlaidConnectionModel(
        guid = guid,
        businessGuid = businessGuid,
        plaidItemId = plaidItemId,
        accessToken = ProtectedString(accessToken),
        isActive = isActive,
        institutionId = institutionId,
        institutionName = institutionName,
      )
    )
  }

  protected fun createPlaidBankAccount(
    businessGuid: UUID,
    connectionGuid: UUID,
    plaidAccountId: String,
    accountName: String? = "Random plaid bank account",
    accountOfficialName: String? = "Official plaid bank account name",
    accountType: AccountType = AccountType.DEPOSITORY,
    accountSubtype: AccountSubtype = AccountSubtype.CHECKING,
    institutionId: String = "ins_1234",
    mask: String = "10009",
  ): PlaidBankAccountModel {
    return get<PlaidBankAccountStore>().upsert(
      PlaidBankAccountModel(
        accountJson = objectMapper.createObjectNode(),
        accountName = accountName,
        accountMask = mask,
        accountOfficialName = accountOfficialName,
        accountOwnerFullName = null,
        accountSubtype = accountSubtype.name,
        accountType = accountType.name,
        currentBalance = null,
        availableBalance = null,
        creditLimit = null,
        businessGuid = businessGuid,
        connectionGuid = connectionGuid,
        currency = "USD",
        institutionId = institutionId,
        plaidAccountId = plaidAccountId,
        plaidProcessorToken = null,
        achRoutingNumber = "**********",
        achAccountNumber = "**********",
        achWireRoutingNumber = "**********",
        isActive = true,
      )
    )
  }

  protected suspend fun assertBankingTier(
    businessGuid: UUID,
    bankingLimitTier: BankAccountSettingRep.BankingLimitTier,
    bankingSupportTier: BankAccountSettingRep.BankingSupportTier,
    bankingPartner: DepositAccountRep.PartnerBank = DepositAccountRep.PartnerBank.Thread,
    checkingDepositProduct: DepositAccountRep.DepositProduct? = null,
    yieldDepositProduct: DepositAccountRep.DepositProduct? = null,
  ) {
    assertThat(
      bankAccountSettingClient.request(
        BankAccountSettingApi.Get(
          businessGuid = businessGuid,
        )
      )
    ).isEqualTo(
      BankAccountSettingRep(
        businessGuid = businessGuid,
        bankingLimitTier = bankingLimitTier,
        bankingSupportTier = bankingSupportTier,
        bankingPartner = bankingPartner,
        checkingDepositProduct = checkingDepositProduct,
        yieldDepositProduct = yieldDepositProduct,
      )
    )
  }
}
