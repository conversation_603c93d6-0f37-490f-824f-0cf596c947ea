package co.highbeam.service.airwallex

import co.highbeam.model.airwallex.AirwallexBankAccountModel
import co.highbeam.store.airwallex.AirwallexBankAccountStore
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class AirwallexBankAccountServiceImpl @Inject constructor(
  private val store: AirwallexBankAccountStore,
) : AirwallexBankAccountService {
  private val logger = KotlinLogging.logger {}

  override fun create(model: AirwallexBankAccountModel): AirwallexBankAccountModel {
    logger.info { "Creating Airwallex bank account for business: ${model.businessGuid}" }
    return store.upsert(model)
  }

  override fun get(guid: UUID): AirwallexBankAccountModel? = store.get(guid)

  override fun getByBusinessGuid(businessGuid: UUID): AirwallexBankAccountModel? =
    store.getByBusinessGuid(businessGuid)

  override fun getByAirwallexAccountId(airwallexAccountId: String): AirwallexBankAccountModel? =
    store.getByAirwallexAccountId(airwallexAccountId)
}
