package co.highbeam.endpoint.airwallexBankAccount

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.airwallexBankAccount.AirwallexBankAccountNotFound
import co.highbeam.mapper.airwallex.AirwallexBankAccountMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.airwallex.AirwallexBankAccountService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.airwallexBankAccount.AirwallexBankAccountApi as Api
import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep as Rep

internal class GetAirwallexBankAccountByBusinessGuid @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: AirwallexBankAccountService,
  private val mapper: AirwallexBankAccountMapper,
) : EndpointHandler<Api.GetByBusinessGuid, Rep>(
  template = Api.GetByBusinessGuid::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByBusinessGuid =
    Api.GetByBusinessGuid(businessGuid = call.getParam("businessGuid"))

  override suspend fun Handler.handle(endpoint: Api.GetByBusinessGuid): Rep {
    authSome(
      authPlatformRole(PlatformRole.SUPERBLOCKS),
      authPermission(Permission.BankAccount_Read) { endpoint.businessGuid }
    )

    val account = service.getByBusinessGuid(endpoint.businessGuid)
      ?: throw AirwallexBankAccountNotFound()

    return mapper.map(account)
  }
}
