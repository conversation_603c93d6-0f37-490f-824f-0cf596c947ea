package co.highbeam.feature.bankAccounts

import co.highbeam.config.AirwallexConfig
import co.highbeam.config.BankingConfig
import co.highbeam.config.CurrencyCloudConfig
import co.highbeam.config.PlaidConfig
import co.highbeam.endpoint.airwallexBankAccount.CreateAirwallexBankAccount
import co.highbeam.endpoint.airwallexBankAccount.GetAirwallexBankAccount
import co.highbeam.endpoint.airwallexBankAccount.GetAirwallexBankAccountByAccountId
import co.highbeam.endpoint.airwallexBankAccount.GetAirwallexBankAccountByBusinessGuid
import co.highbeam.endpoint.bankAccount.CloseBankAccount
import co.highbeam.endpoint.bankAccount.GetAllBankAccountsByBusinessGuid
import co.highbeam.endpoint.bankAccount.GetBankAccount
import co.highbeam.endpoint.bankAccount.GetBankAccountByBusinessGuid
import co.highbeam.endpoint.bankAccount.GetBankAccountByUnitCoAccountId
import co.highbeam.endpoint.bankAccount.GetBankAccountSummary
import co.highbeam.endpoint.bankAccount.GetPrimaryBankAccountByBusinessGuid
import co.highbeam.endpoint.bankAccount.GetPrimaryBankAccounts
import co.highbeam.endpoint.bankAccount.MigrateBankAccounts
import co.highbeam.endpoint.bankAccount.PatchBankAccount
import co.highbeam.endpoint.bankAccount.PostBankAccount
import co.highbeam.endpoint.bankAccount.PostBankAccountInternal
import co.highbeam.endpoint.bankAccount.UpdateBankAccountInterestTierByBusiness
import co.highbeam.endpoint.bankAccountSetting.CreateBankAccountSetting
import co.highbeam.endpoint.bankAccountSetting.GetBankingAccountSetting
import co.highbeam.endpoint.bankAccountSetting.SetBankingAccountSetting
import co.highbeam.endpoint.bankAccountSetting.SetBankingTier
import co.highbeam.endpoint.internationalBankAccount.CreateInternationalBankAccount
import co.highbeam.endpoint.internationalBankAccount.DisableInternationalBankAccount
import co.highbeam.endpoint.internationalBankAccount.EnableInternationalBankAccount
import co.highbeam.endpoint.internationalBankAccount.GetInternationalBankAccountsByBusinessGuid
import co.highbeam.endpoint.internationalBankAccount.GetInternationalBankAccountsByCurrencyCloudBankAccountGuid
import co.highbeam.endpoint.internationalBankAccount.PatchInternationalBankAccount
import co.highbeam.endpoint.internationalBankAccount.PatchInternationalBankAccountTermsAcceptedAt
import co.highbeam.endpoint.internationalBankAccount.SyncInternationalBankAccountFromUnit
import co.highbeam.endpoint.plaid.CreatePlaidAccessToken
import co.highbeam.endpoint.plaid.CreatePlaidBankAccountSyncTask
import co.highbeam.endpoint.plaid.CreatePlaidLinkToken
import co.highbeam.endpoint.plaid.DeletePlaidBankAccount
import co.highbeam.endpoint.plaid.DeletePlaidConnection
import co.highbeam.endpoint.plaid.ExecutePlaidBankAccountSyncTask
import co.highbeam.endpoint.plaid.GetPlaidConnection
import co.highbeam.endpoint.plaid.GetPlaidConnectionsByBusiness
import co.highbeam.endpoint.plaid.HandlePlaidWebhook
import co.highbeam.endpoint.plaid.RefreshPlaidBalances
import co.highbeam.endpoint.transaction.GetInvoice
import co.highbeam.endpoint.transaction.SearchTransactions
import co.highbeam.endpoint.transaction.SearchTransactionsCsv
import co.highbeam.endpoint.transaction.SearchTransactionsQbo
import co.highbeam.endpoint.transaction.SetInvoice
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.feature.Feature
import co.highbeam.listener.bankAccountSettings.BankingLimitTierUpgradeListener
import co.highbeam.listener.bankAccounts.AggregateBankAccountInterestTierListener
import co.highbeam.listener.bankAccounts.BankAccountInterestTierJobListener
import co.highbeam.listener.bankAccounts.BusinessApprovedListener
import co.highbeam.listener.bankAccounts.InternationalBankAccountCreationListener
import co.highbeam.listener.plaid.PlaidAccountCreationListener
import co.highbeam.listener.plaid.PlaidWebhookListener
import co.highbeam.publisher.bankAccountSetting.BankingTierVipUpgradePublisherFactory
import co.highbeam.publisher.bankAccounts.AggregateBankAccountInterestTierPublisherFactory
import co.highbeam.publisher.bankAccounts.BankAccountInterestTierJobPublisherFactory
import co.highbeam.publisher.bankAccounts.CashbackEmailPublisherFactory
import co.highbeam.publisher.bankAccounts.DepositInterestEmailPublisherFactory
import co.highbeam.publisher.bankAccounts.InternationalBankAccountCreationPublisherFactory
import co.highbeam.publisher.bankAccounts.PlaidAccountCreationPublisherFactory
import co.highbeam.publisher.bankAccounts.PlaidAccountDeactivatedPublisherFactory
import co.highbeam.publisher.bankAccounts.PlaidAccountHardDeletedPublisherFactory
import co.highbeam.publisher.bankAccounts.PlaidTransactionSyncFilePublisherFactory
import co.highbeam.publisher.bankAccounts.PlaidTransactionSyncPublisherFactory
import co.highbeam.publisher.plaid.PlaidWebhookPublisherFactory
import co.highbeam.service.cashback.CashbackEmailer
import co.highbeam.service.depositInterest.DepositInterestEmailer
import com.google.inject.Injector
import com.google.inject.Key
import com.google.inject.TypeLiteral
import co.highbeam.endpoint.plaid.GetBankAccountsByBusiness as GetPlaidBankAccountsByBusiness

class BankAccountsFeature(
  private val bankingConfig: BankingConfig,
  private val currencyCloud: CurrencyCloudConfig,
  private val airwallexConfig: AirwallexConfig,
  private val plaid: PlaidConfig
) : Feature() {
  private val publishers: MutableSet<TypeLiteral<out EventPublisher<*>>> = mutableSetOf()

  override fun bind() {
    bindConfig()
    bindApiEndpoints()
    bindWebhooks()
    bindPublishers()
    bindListeners()
  }

  private fun bindConfig() {
    bind(BankingConfig::class.java).toInstance(bankingConfig)
    bind(CurrencyCloudConfig::class.java).toInstance(currencyCloud)
    bind(AirwallexConfig::class.java).toInstance(airwallexConfig)
    bind(PlaidConfig::class.java).toInstance(plaid)
  }

  private fun bindApiEndpoints() {
    bind(PostBankAccount::class.java).asEagerSingleton()
    bind(PostBankAccountInternal::class.java).asEagerSingleton()
    bind(GetBankAccount::class.java).asEagerSingleton()
    bind(GetBankAccountSummary::class.java).asEagerSingleton()
    bind(GetBankAccountByBusinessGuid::class.java).asEagerSingleton()
    bind(GetBankAccountByUnitCoAccountId::class.java).asEagerSingleton()
    bind(CloseBankAccount::class.java).asEagerSingleton()
    bind(UpdateBankAccountInterestTierByBusiness::class.java).asEagerSingleton()

    bind(CreateBankAccountSetting::class.java).asEagerSingleton()
    bind(GetBankingAccountSetting::class.java).asEagerSingleton()
    bind(SetBankingAccountSetting::class.java).asEagerSingleton()
    bind(SetBankingTier::class.java).asEagerSingleton()

    bind(GetAllBankAccountsByBusinessGuid::class.java).asEagerSingleton()
    bind(GetPrimaryBankAccountByBusinessGuid::class.java).asEagerSingleton()
    bind(GetPrimaryBankAccounts::class.java).asEagerSingleton()
    bind(PatchBankAccount::class.java).asEagerSingleton()

    bind(CreateAirwallexBankAccount::class.java).asEagerSingleton()
    bind(GetAirwallexBankAccount::class.java).asEagerSingleton()
    bind(GetAirwallexBankAccountByAccountId::class.java).asEagerSingleton()
    bind(GetAirwallexBankAccountByBusinessGuid::class.java).asEagerSingleton()

    bind(CreateInternationalBankAccount::class.java).asEagerSingleton()
    bind(PatchInternationalBankAccount::class.java).asEagerSingleton()
    bind(PatchInternationalBankAccountTermsAcceptedAt::class.java).asEagerSingleton()
    bind(SyncInternationalBankAccountFromUnit::class.java).asEagerSingleton()
    bind(GetInternationalBankAccountsByBusinessGuid::class.java).asEagerSingleton()
    bind(GetInternationalBankAccountsByCurrencyCloudBankAccountGuid::class.java).asEagerSingleton()
    bind(DisableInternationalBankAccount::class.java).asEagerSingleton()
    bind(EnableInternationalBankAccount::class.java).asEagerSingleton()

    bind(SetInvoice::class.java).asEagerSingleton()
    bind(GetInvoice::class.java).asEagerSingleton()

    bind(SearchTransactions::class.java).asEagerSingleton()
    bind(SearchTransactionsCsv::class.java).asEagerSingleton()
    bind(SearchTransactionsQbo::class.java).asEagerSingleton()

    bind(CreatePlaidLinkToken::class.java).asEagerSingleton()
    bind(CreatePlaidAccessToken::class.java).asEagerSingleton()
    bind(GetPlaidBankAccountsByBusiness::class.java).asEagerSingleton()
    bind(GetPlaidConnection::class.java).asEagerSingleton()
    bind(GetPlaidConnectionsByBusiness::class.java).asEagerSingleton()
    bind(DeletePlaidConnection::class.java).asEagerSingleton()
    bind(DeletePlaidBankAccount::class.java).asEagerSingleton()
    bind(RefreshPlaidBalances::class.java).asEagerSingleton()

    bind(MigrateBankAccounts::class.java).asEagerSingleton()

    bind(CreatePlaidBankAccountSyncTask::class.java).asEagerSingleton()
    bind(ExecutePlaidBankAccountSyncTask::class.java).asEagerSingleton()
  }

  private fun bindPublishers() {
    CashbackEmailPublisherFactory.bind(binder(), publishers)
    DepositInterestEmailPublisherFactory.bind(binder(), publishers)

    PlaidAccountCreationPublisherFactory.bind(binder(), publishers)
    PlaidAccountDeactivatedPublisherFactory.bind(binder(), publishers)
    PlaidAccountHardDeletedPublisherFactory.bind(binder(), publishers)
    PlaidTransactionSyncFilePublisherFactory.bind(binder(), publishers)
    PlaidTransactionSyncPublisherFactory.bind(binder(), publishers)
    PlaidWebhookPublisherFactory.bind(binder(), publishers)

    BankingTierVipUpgradePublisherFactory.bind(binder(), publishers)

    AggregateBankAccountInterestTierPublisherFactory.bind(binder(), publishers)
    BankAccountInterestTierJobPublisherFactory.bind(binder(), publishers)
    InternationalBankAccountCreationPublisherFactory.bind(binder(), publishers)
  }

  private fun bindListeners() {
    bind(AggregateBankAccountInterestTierListener::class.java)
    bind(BankingLimitTierUpgradeListener::class.java)
    bind(BusinessApprovedListener::class.java)
    bind(CashbackEmailer::class.java)
    bind(DepositInterestEmailer::class.java)

    bind(PlaidAccountCreationListener::class.java)
    bind(PlaidWebhookListener::class.java)

    bind(BankAccountInterestTierJobListener::class.java)
    bind(InternationalBankAccountCreationListener::class.java)
  }

  private fun bindWebhooks() {
    bind(HandlePlaidWebhook::class.java).asEagerSingleton()
  }

  override fun stop(injector: Injector) {
    publishers.forEach { injector.getInstance(Key.get(it)).close() }
  }
}
