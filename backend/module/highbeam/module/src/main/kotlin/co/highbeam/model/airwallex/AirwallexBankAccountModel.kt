package co.highbeam.model.airwallex

import java.util.UUID

internal data class AirwallexBankAccountModel(
  val guid: UUID,
  val businessGuid: UUID,
  val enabled: Boolean,
  val airwallexAccountId: String,
  val airwallexWalletId: String?,
  val achRoutingNumber: String,
  val achAccountNumber: String,
  val wireRoutingNumber: String,
  val wireAccountNumber: String,
  val iban: String?,
  val swiftCode: String?,
)
