package co.highbeam.config

import co.highbeam.protectedString.ProtectedString
import com.currencycloud.client.CurrencyCloudClient
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

data class CurrencyCloudConfig(
  val loginId: String,
  @JsonDeserialize(using = ProtectedConfigStringDeserializer::class)
  val apiKey: ProtectedString,
  val environment: Environment,
  @JsonDeserialize(using = ProtectedConfigStringDeserializer::class)
  val webhookKey: ProtectedString,
  val slackWebhookPath: String,
  val paymentFeeId: String,
) {
  enum class Environment(val currencyCloudEnvironment: CurrencyCloudClient.Environment) {
    Demo(CurrencyCloudClient.Environment.demo),
    Production(CurrencyCloudClient.Environment.production),
  }
}

