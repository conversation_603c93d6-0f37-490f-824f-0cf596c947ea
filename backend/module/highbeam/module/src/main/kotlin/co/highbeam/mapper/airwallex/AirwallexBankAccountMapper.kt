package co.highbeam.mapper.airwallex

import co.highbeam.model.airwallex.AirwallexBankAccountModel
import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject

internal class AirwallexBankAccountMapper @Inject constructor(
  private val uuidGenerator: UuidGenerator,
) {
  fun map(rep: AirwallexBankAccountRep.Creator): AirwallexBankAccountModel {
    return AirwallexBankAccountModel(
      guid = uuidGenerator.generate(),
      businessGuid = rep.businessGuid,
      enabled = rep.enabled,
      airwallexAccountId = rep.airwallexAccountId,
      airwallexWalletId = rep.airwallexWalletId,
      achRoutingNumber = rep.achRoutingNumber,
      achAccountNumber = rep.achAccountNumber,
      wireRoutingNumber = rep.wireRoutingNumber,
      wireAccountNumber = rep.wireAccountNumber,
      iban = rep.iban,
      swiftCode = rep.swiftCode,
    )
  }

  fun map(model: AirwallexBankAccountModel): AirwallexBankAccountRep {
    return AirwallexBankAccountRep(
      guid = model.guid,
      businessGuid = model.businessGuid,
      enabled = model.enabled,
      airwallexAccountId = model.airwallexAccountId,
      airwallexWalletId = model.airwallexWalletId,
      achRoutingNumber = model.achRoutingNumber,
      achAccountNumber = model.achAccountNumber,
      wireRoutingNumber = model.wireRoutingNumber,
      wireAccountNumber = model.wireAccountNumber,
      iban = model.iban,
      swiftCode = model.swiftCode,
    )
  }
}
