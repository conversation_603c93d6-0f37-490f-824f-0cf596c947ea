package co.highbeam.service.airwallex

import co.highbeam.model.airwallex.AirwallexBankAccountModel
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(AirwallexBankAccountServiceImpl::class)
internal interface AirwallexBankAccountService {
  fun create(model: AirwallexBankAccountModel): AirwallexBankAccountModel

  fun get(guid: UUID): AirwallexBankAccountModel?

  fun getByBusinessGuid(businessGuid: UUID): AirwallexBankAccountModel?

  fun getByAirwallexAccountId(airwallexAccountId: String): AirwallexBankAccountModel?
}
