package co.highbeam.config

import co.highbeam.protectedString.ProtectedString
import com.airwallex.client.AirwallexHttpClient
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

data class AirwallexConfig(
  val environment: AirwallexHttpClient.Environment,
  @JsonDeserialize(using = ProtectedConfigStringDeserializer::class)
  val apiKey: ProtectedString,
  val clientId: String,
  val slackWebhookPath: String,
)
