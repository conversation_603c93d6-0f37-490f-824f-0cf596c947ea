package co.highbeam.store.airwallex

import co.highbeam.model.airwallex.AirwallexBankAccountModel
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

@Singleton
internal class AirwallexBankAccountStore @Inject constructor(
  jdbi: Jdbi,
) : SqlStore(jdbi) {
  fun upsert(model: AirwallexBankAccountModel): AirwallexBankAccountModel = transaction { handle ->
    val query = handle.createQuery(sqlResource("store/airwallexBankAccount/upsert.sql"))
    query.bindKotlin(model)
    return@transaction query.mapTo(AirwallexBankAccountModel::class.java).single()
  }

  fun get(guid: UUID): AirwallexBankAccountModel? = handle { handle ->
    val query = handle.createQuery(sqlResource("store/airwallexBankAccount/get.sql"))
    query.bind("guid", guid)
    return@handle query.mapTo(AirwallexBankAccountModel::class.java).singleOrNull()
  }

  fun getByBusinessGuid(businessGuid: UUID): AirwallexBankAccountModel? = handle { handle ->
    val query = handle.createQuery(sqlResource("store/airwallexBankAccount/getByBusinessGuid.sql"))
    query.bind("businessGuid", businessGuid)
    return@handle query.mapTo(AirwallexBankAccountModel::class.java).singleOrNull()
  }

  fun getByAirwallexAccountId(airwallexAccountId: String): AirwallexBankAccountModel? =
    handle { handle ->
      val query =
        handle.createQuery(sqlResource("store/airwallexBankAccount/getByAirwallexAccountId.sql"))
      query.bind("airwallexAccountId", airwallexAccountId)
      return@handle query.mapTo(AirwallexBankAccountModel::class.java).singleOrNull()
    }
}
