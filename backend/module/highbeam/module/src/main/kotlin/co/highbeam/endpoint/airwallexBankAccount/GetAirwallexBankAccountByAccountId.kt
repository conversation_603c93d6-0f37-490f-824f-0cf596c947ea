package co.highbeam.endpoint.airwallexBankAccount

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.exception.airwallexBankAccount.AirwallexBankAccountNotFound
import co.highbeam.mapper.airwallex.AirwallexBankAccountMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.airwallex.AirwallexBankAccountService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.airwallexBankAccount.AirwallexBankAccountApi as Api
import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep as Rep

internal class GetAirwallexBankAccountByAccountId @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: AirwallexBankAccountService,
  private val mapper: AirwallexBankAccountMapper,
) : EndpointHandler<Api.GetByAirwallexAccountId, Rep>(
  template = Api.GetByAirwallexAccountId::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByAirwallexAccountId =
    Api.GetByAirwallexAccountId(airwallexAccountId = call.getParam("airwallexAccountId"))

  override suspend fun Handler.handle(endpoint: Api.GetByAirwallexAccountId): Rep {
    authSome(authPlatformRole(PlatformRole.SUPERBLOCKS))

    val account = service.getByAirwallexAccountId(endpoint.airwallexAccountId)
      ?: throw AirwallexBankAccountNotFound()
    return mapper.map(account)
  }
}
