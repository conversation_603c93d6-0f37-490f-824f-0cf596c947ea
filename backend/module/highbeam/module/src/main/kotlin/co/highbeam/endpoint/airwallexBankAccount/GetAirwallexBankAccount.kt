package co.highbeam.endpoint.airwallexBankAccount

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.exception.airwallexBankAccount.AirwallexBankAccountNotFound
import co.highbeam.mapper.airwallex.AirwallexBankAccountMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.airwallex.AirwallexBankAccountService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.airwallexBankAccount.AirwallexBankAccountApi as Api
import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep as Rep

internal class GetAirwallexBankAccount @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: AirwallexBankAccountService,
  private val mapper: AirwallexBankAccountMapper,
) : EndpointHandler<Api.Get, Rep>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(guid = call.getParam("guid"))

  override suspend fun Handler.handle(endpoint: Api.Get): Rep {
    authSome(authPlatformRole(PlatformRole.SUPERBLOCKS))

    val account = service.get(endpoint.guid) ?: throw AirwallexBankAccountNotFound()
    return mapper.map(account)
  }
}
