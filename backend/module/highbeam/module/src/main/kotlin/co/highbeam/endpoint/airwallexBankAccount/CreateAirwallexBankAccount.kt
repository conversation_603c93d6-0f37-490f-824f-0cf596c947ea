package co.highbeam.endpoint.airwallexBankAccount

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.mapper.airwallex.AirwallexBankAccountMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.airwallex.AirwallexBankAccountService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.airwallexBankAccount.AirwallexBankAccountApi as Api
import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep as Rep

internal class CreateAirwallexBankAccount @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: AirwallexBankAccountService,
  private val mapper: AirwallexBankAccountMapper,
) : EndpointHandler<Api.Create, Rep>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create(rep = call.body())

  override suspend fun Handler.handle(endpoint: Api.Create): Rep {
    authSome(authPlatformRole(PlatformRole.SUPERBLOCKS))

    val model = mapper.map(endpoint.rep)
    return mapper.map(service.create(model))
  }
}
