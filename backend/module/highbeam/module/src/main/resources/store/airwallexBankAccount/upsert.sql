insert into bank_accounts.airwallex_bank_account (guid,
                                                  business_guid,
                                                  airwallex_account_id,
                                                  airwallex_wallet_id,
                                                  ach_routing_number,
                                                  ach_account_number,
                                                  wire_routing_number,
                                                  wire_account_number,
                                                  iban,
                                                  swift_code,
                                                  enabled)
values (:guid,
        :businessGuid,
        :airwallexAccountId,
        :airwallexWalletId,
        :achRoutingNumber,
        :achAccountNumber,
        :wireRoutingNumber,
        :wireAccountNumber,
        :iban,
        :swiftCode,
        :enabled)
on conflict
  on constraint uniq__airwallex_bank_account__business_guid
  do update
  set airwallex_account_id = :airwallexAccountId,
      airwallex_wallet_id  = :airwallexWalletId,
      ach_routing_number   = :achRoutingNumber,
      ach_account_number   = :achAccountNumber,
      wire_routing_number  = :wireRoutingNumber,
      wire_account_number  = :wireAccountNumber,
      iban                 = :iban,
      swift_code           = :swiftCode,
      enabled              = :enabled
returning *
