package co.highbeam.rep.payment

import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import co.highbeam.validation.Validator
import co.highbeam.validation.ifPresent
import java.time.ZonedDateTime
import java.util.UUID

data class AirwallexPaymentRep(
  val amount: Money,
  val createdAt: ZonedDateTime,
  val currency: String,
  val externalPaymentId: String?,
  val paymentFee: Money?,
  val paymentFeeCurrency: String?,
  val generalPaymentMetadataGuid: UUID,
  val reason: String,
  val status: String,
  val transferMethod: TransferMethod,
) : CompleteRep {
  enum class TransferMethod(val value: String) { Swift("SWIFT"), Local("LOCAL") }

  data class Creator(
    val amount: Money,
    val bankAccountGuid: UUID,
    val businessGuid: UUID,
    val payeeGuid: UUID,
    val description: String,
    val idempotencyKey: UUID,
    val reason: String,
    val payeeEmailToggle: Boolean = false,
    val payeeEmail: String?,
    val paymentGuid: UUID?,
    val generalPaymentMetadataGuid: UUID?,
    val receivedCurrency: String = "USD",
    val receivedAmount: Money,
    val buyRate: Double? = null,
    val purposeCode: String? = null,
    val invoiceNumber: String? = null,
    val invoiceDate: ZonedDateTime? = null,
    val tags: Map<String, String>,
    val transferMethod: TransferMethod,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Creator::receivedCurrency) { ifPresent { Validator.currencyCode(this) } }
      validate(Creator::description) { length < 500 }
      validate(Creator::reason) { length < 500 }
      validate(Creator::payeeEmail) {
        ifPresent { Validator.emailAddress(this) }
      }
      validate(Creator::purposeCode) {
        ifPresent { length < 100 }
      }
      validate(Creator::invoiceNumber) {
        ifPresent { length < 100 }
      }
    }
  }
}
