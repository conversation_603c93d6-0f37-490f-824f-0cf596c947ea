package co.highbeam.mapper.payment

import co.highbeam.mapper.payment.InternationalPaymentUnitCoMapper.toInlineCounterParty
import co.highbeam.mapper.payment.InternationalPaymentUnitCoMapper.trimToUnitCoAchAddenda
import co.highbeam.mapper.payment.InternationalPaymentUnitCoMapper.trimToUnitCoAchDescription
import co.highbeam.mapper.payment.InternationalPaymentUnitCoMapper.trimToUnitCoWireDescription
import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.payment.AchPaymentRep
import co.highbeam.rep.payment.AirwallexPaymentRep
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_PRIORITY_FEE
import co.highbeam.rep.payment.WirePaymentRep
import co.unit.rep.UnitCoAchPaymentRep
import co.unit.rep.UnitCoPayment
import co.unit.rep.UnitCoWirePaymentRep
import java.util.UUID

object AirwallexPaymentUnitCoMapper {
  fun AirwallexPaymentRep.Creator.toUnitCoAchCreation(
    bankAccount: BankAccountRep.Complete,
    inlineCounterparty: AchPaymentRep.InlineCounterparty,
    generalPaymentMetadataGuid: UUID,
    payeeGuid: UUID,
    paymentGuid: UUID?,
    payeeEmail: String?,
    sameDay: Boolean = false,
  ): UnitCoAchPaymentRep.Creator = UnitCoAchPaymentRep.Creator(
    amount = this.amountWithFees(),
    direction = MoneyDirection.Credit,
    counterparty = inlineCounterparty.toInlineCounterParty(),
    description = trimToUnitCoAchDescription(this.description),
    addenda = trimToUnitCoAchAddenda(this.reason),
    generalPaymentMetadataGuid = generalPaymentMetadataGuid,
    idempotencyKey = this.idempotencyKey,
    payeeGuid = payeeGuid,
    fromAccountType = bankAccount.type,
    fromAccountId = bankAccount.unitCoDepositAccountId,
    paymentGuid = paymentGuid,
    paymentType = UnitCoPayment.PaymentType.InternationalWire,
    payeeEmail = payeeEmail,
    sameDay = sameDay,
    tags = this.tags,
  )

  fun AirwallexPaymentRep.Creator.toUnitCoWireCreation(
    bankAccount: BankAccountRep.Complete,
    inlineCounterparty: WirePaymentRep.InlineCounterparty,
    payeeGuid: UUID,
    paymentGuid: UUID?,
    payeeEmail: String?,
    generalPaymentMetadataGuid: UUID,
  ): UnitCoWirePaymentRep.Creator = UnitCoWirePaymentRep.Creator(
    amount = this.amountWithFees(),
    inlineCounterparty = inlineCounterparty.toInlineCounterParty(),
    description = trimToUnitCoWireDescription(this.description + " | " + this.reason),
    idempotencyKey = this.idempotencyKey,
    payeeGuid = payeeGuid,
    fromAccountType = bankAccount.type,
    fromAccountId = bankAccount.unitCoDepositAccountId,
    paymentGuid = paymentGuid,
    paymentType = UnitCoPayment.PaymentType.InternationalWire,
    payeeEmail = payeeEmail,
    generalPaymentMetadataGuid = generalPaymentMetadataGuid,
    tags = this.tags,
  )

  fun AirwallexPaymentRep.Creator.amountWithFees(): Money {
    val fee = if (this.receivedCurrency == "USD") {
      INTERNATIONAL_WIRE_PRIORITY_FEE
    } else {
      Money(0)
    }

    return this.amount + fee
  }
}
