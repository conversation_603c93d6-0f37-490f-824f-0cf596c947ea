package co.highbeam.model.payeePaymentMethod

import co.highbeam.rep.TransferRep
import co.highbeam.rep.payee.PayeeRep.EntityType
import co.highbeam.rep.payment.InternationalPaymentRep
import java.util.UUID

internal data class PayeePaymentMethodModel(
  val guid: UUID,
  val businessGuid: UUID,
  val payeeGuid: UUID,
  val paymentMethod: TransferRep.PaymentMethod,

  val addressLine1: String? = null,
  val addressLine2: String? = null,
  val city: String? = null,
  val state: String? = null,
  val zipCode: String? = null,
  val country: String? = null,

  // Ach
  val achAccountNumber: String? = null,
  val achRoutingNumber: String? = null,

  // Wire
  val wireAccountNumber: String? = null,
  val wireRoutingNumber: String? = null,

  // International
  val entityType: EntityType? = null,
  val firstName: String? = null,
  val lastName: String? = null,
  val companyName: String? = null,
  val companyBankHolderName: String? = null,

  val internationalBankCountry: String? = null,
  val internationalAccountNumber: String? = null,
  val internationalBankCode: String? = null,
  val internationalBranchCode: String? = null,
  val internationalBsbCode: String? = null,
  val internationalClabe: String? = null,
  val internationalCnaps: String? = null,
  val internationalIban: String? = null,
  val internationalIfsc: String? = null,
  val internationalSortCode: String? = null,
  val internationalSwiftCode: String? = null,
  val internationalDeliveryMethod: InternationalPaymentRep.PaymentType? = null,
  val currencyCloudBeneficiaryId: UUID? = null,
  val currency: String? = "USD",

  val airwallexBeneficiaryId: String? = null,
) {
  data class Update(
    val payeeGuid: UUID,
    val paymentMethod: TransferRep.PaymentMethod,

    val addressLine1: String? = null,
    val addressLine2: String? = null,
    val city: String? = null,
    val state: String? = null,
    val zipCode: String? = null,
    val country: String? = null,

    // Ach
    val achAccountNumber: String? = null,
    val achRoutingNumber: String? = null,

    // International
    val entityType: EntityType? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val companyName: String? = null,
    val companyBankHolderName: String? = null,

    val internationalBankCountry: String? = null,
    val internationalAccountNumber: String? = null,
    val internationalBankCode: String? = null,
    val internationalBranchCode: String? = null,
    val internationalBsbCode: String? = null,
    val internationalClabe: String? = null,
    val internationalCnaps: String? = null,
    val internationalIban: String? = null,
    val internationalIfsc: String? = null,
    val internationalSwiftCode: String? = null,
    val internationalDeliveryMethod: InternationalPaymentRep.PaymentType? = null,
    val currencyCloudBeneficiaryId: UUID? = null,
    val currency: String? = null,
  )
}
