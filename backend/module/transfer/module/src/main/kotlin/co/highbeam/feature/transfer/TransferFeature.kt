package co.highbeam.feature.transfer

import co.highbeam.endpoint.currencyCloudWebhook.CURRENCY_CLOUD_WEBHOOK_TOKEN
import co.highbeam.endpoint.currencyCloudWebhook.CurrencyCloudWebhook
import co.highbeam.endpoint.institution.GetInstitution
import co.highbeam.endpoint.payee.CreatePayee
import co.highbeam.endpoint.payee.DeletePayee
import co.highbeam.endpoint.payee.GetPayee
import co.highbeam.endpoint.payee.GetPayeesByBusiness
import co.highbeam.endpoint.payee.UpdatePayee
import co.highbeam.endpoint.payee.ValidatePayee
import co.highbeam.endpoint.payment.CreateAchPayment
import co.highbeam.endpoint.payment.CreateInternationalWirePayment
import co.highbeam.endpoint.payment.CreateInternationalWireReturnPayment
import co.highbeam.endpoint.payment.CreatePayment
import co.highbeam.endpoint.payment.CreateScheduledAchPayment
import co.highbeam.endpoint.payment.CreateWirePayment
import co.highbeam.endpoint.payment.GetInternationalWireQuote
import co.highbeam.endpoint.paymentDetails.BatchGetPaymentDetails
import co.highbeam.endpoint.paymentDetails.GetPaymentDetails
import co.highbeam.endpoint.paymentDetails.GetPaymentReceipt
import co.highbeam.endpoint.paymentMetadata.BatchGetGeneralPaymentMetadata
import co.highbeam.endpoint.paymentMetadata.CreateGeneralPaymentMetadata
import co.highbeam.endpoint.paymentMetadata.DeleteInvoiceGeneralPaymentMetadata
import co.highbeam.endpoint.paymentMetadata.GetGeneralPaymentMetadata
import co.highbeam.endpoint.paymentMetadata.UpdateGeneralPaymentMetadata
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.feature.Feature
import co.highbeam.listener.CurrencyCloudWebhookListener
import co.highbeam.listener.UnitCoWebhookCheckPaymentListener
import co.highbeam.listener.UnitCoWebhookPaymentListener
import co.highbeam.listener.UnitCoWebhookReceivedPaymentListener
import co.highbeam.protectedString.ProtectedString
import co.highbeam.publishers.CurrencyCloudWebhookPublisherFactory
import co.highbeam.service.payment.AIRWALLEX_SLACK_WEBHOOK_PATH
import co.highbeam.service.payment.CURRENCY_CLOUD_SLACK_WEBHOOK_PATH
import com.google.inject.TypeLiteral
import com.google.inject.name.Names

class TransferFeature(
  private val currencyCloudWebhookToken: ProtectedString,
  private val currencyCloudSlackWebhookPath: String,
  private val airwallexSlackWebhookPath: String,
) : Feature() {
  private val publishers: MutableSet<TypeLiteral<out EventPublisher<*>>> = mutableSetOf()
  override fun bind() {
    bindConfig()
    bindApiEndpoints()
    bindPublishers()
    bindListeners()
    bindWebhooks()
  }

  private fun bindConfig() {
    bind(ProtectedString::class.java).annotatedWith(Names.named(CURRENCY_CLOUD_WEBHOOK_TOKEN))
      .toInstance(currencyCloudWebhookToken)
    bind(String::class.java).annotatedWith(Names.named(CURRENCY_CLOUD_SLACK_WEBHOOK_PATH))
      .toInstance(currencyCloudSlackWebhookPath)
    bind(String::class.java).annotatedWith(Names.named(AIRWALLEX_SLACK_WEBHOOK_PATH))
      .toInstance(airwallexSlackWebhookPath)
  }

  private fun bindApiEndpoints() {
    bind(CreatePayee::class.java).asEagerSingleton()
    bind(GetPayee::class.java).asEagerSingleton()
    bind(GetPayeesByBusiness::class.java).asEagerSingleton()
    bind(UpdatePayee::class.java).asEagerSingleton()
    bind(DeletePayee::class.java).asEagerSingleton()

    bind(ValidatePayee::class.java).asEagerSingleton()

    bind(GetPaymentDetails::class.java).asEagerSingleton()
    bind(GetPaymentReceipt::class.java).asEagerSingleton()
    bind(BatchGetPaymentDetails::class.java).asEagerSingleton()

    bind(GetInstitution::class.java).asEagerSingleton()

    bind(CreateInternationalWirePayment::class.java).asEagerSingleton()
    bind(CreateInternationalWireReturnPayment::class.java).asEagerSingleton()
    bind(GetInternationalWireQuote::class.java).asEagerSingleton()

    bind(CreateAchPayment::class.java).asEagerSingleton()
    bind(CreateScheduledAchPayment::class.java).asEagerSingleton()
    bind(CreateWirePayment::class.java).asEagerSingleton()
    bind(CreatePayment::class.java).asEagerSingleton()

    bind(BatchGetGeneralPaymentMetadata::class.java).asEagerSingleton()
    bind(CreateGeneralPaymentMetadata::class.java).asEagerSingleton()
    bind(GetGeneralPaymentMetadata::class.java).asEagerSingleton()
    bind(UpdateGeneralPaymentMetadata::class.java).asEagerSingleton()
    bind(DeleteInvoiceGeneralPaymentMetadata::class.java).asEagerSingleton()
  }

  private fun bindListeners() {
    bind(CurrencyCloudWebhookListener::class.java)
    bind(UnitCoWebhookCheckPaymentListener::class.java)
    bind(UnitCoWebhookPaymentListener::class.java)
    bind(UnitCoWebhookReceivedPaymentListener::class.java)
  }

  private fun bindPublishers() {
    CurrencyCloudWebhookPublisherFactory.bind(binder(), publishers)
  }

  private fun bindWebhooks() {
    bind(CurrencyCloudWebhook::class.java).asEagerSingleton()
  }
}
