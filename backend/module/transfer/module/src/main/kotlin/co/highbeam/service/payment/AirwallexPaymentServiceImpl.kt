package co.highbeam.service.payment

import co.highbeam.api.airwallexBankAccount.AirwallexBankAccountApi
import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.client.airwallexBankAccount.AirwallexBankAccountClient
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.exception.airwallexBankAccount.AirwallexBankAccountNotFound
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.business.BusinessIsNotOnboardedWithHighbeamYet
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.payment.AirwallexNegativeFeeException
import co.highbeam.exception.payment.InternationalQuoteNotFound
import co.highbeam.exception.payment.InternationalWireFundingCancelFailed
import co.highbeam.exception.payment.InternationalWireFundingFailed
import co.highbeam.exception.payment.InternationalWirePaymentMethodCurrencyMismatch
import co.highbeam.exception.payment.InternationalWirePaymentMethodNotFound
import co.highbeam.exception.payment.InternationalWireQuoteMismatch
import co.highbeam.exception.payment.InternationalWireQuoteNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.mapper.payment.AirwallexPaymentUnitCoMapper.amountWithFees
import co.highbeam.mapper.payment.AirwallexPaymentUnitCoMapper.toUnitCoAchCreation
import co.highbeam.mapper.payment.AirwallexPaymentUnitCoMapper.toUnitCoWireCreation
import co.highbeam.model.payeePaymentMethod.PayeePaymentMethodModel
import co.highbeam.money.Money
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.TransferRep
import co.highbeam.rep.airwallexBankAccount.AirwallexBankAccountRep
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.payment.AchPaymentRep
import co.highbeam.rep.payment.AirwallexPaymentRep
import co.highbeam.rep.payment.CURRENCY_RATE
import co.highbeam.rep.payment.CURRENCY_RATE_EPSILON
import co.highbeam.rep.payment.ENABLED_BUY_CURRENCIES
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_FUNDING_WIRE_ADDRESS
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_FUNDING_WIRE_REQUIREMENT
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_PRIORITY_FEE
import co.highbeam.rep.payment.InternationalQuoteRep
import co.highbeam.rep.payment.WirePaymentRep
import co.highbeam.service.payeePaymentMethod.PayeePaymentMethodService
import co.highbeam.util.uuid.UuidGenerator
import co.highbeam.util.uuid.asByteArray
import co.unit.client.UnitCoClient
import co.unit.rep.UnitCoAchPaymentRep
import co.unit.rep.UnitCoWirePaymentRep
import co.unit.rep.UnitPaymentRep
import com.airwallex.client.AirwallexClient
import com.airwallex.rep.AirwallexTransferRep
import com.airwallex.rep.FxQuoteRep
import com.google.inject.Inject
import mu.KotlinLogging
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Clock
import java.time.ZonedDateTime
import java.util.Currency
import java.util.UUID
import kotlin.math.abs

@Suppress("LargeClass", "TooManyFunctions")
internal class AirwallexPaymentServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val businessClient: BusinessClient,
  private val clock: Clock,
  private val airwallexClient: AirwallexClient,
  private val airwallexBankAccountClient: AirwallexBankAccountClient,
  private val paymentService: PaymentService,
  private val payeePaymentMethodService: PayeePaymentMethodService,
  private val unitCoClient: UnitCoClient,
  private val uuidGenerator: UuidGenerator,
) : AirwallexPaymentService {
  private val logger = KotlinLogging.logger {}

  @Suppress("LongMethod")
  override suspend fun createAirwallexPayment(
    creator: AirwallexPaymentRep.Creator
  ): AirwallexPaymentRep {
    val business =
      businessClient.request(BusinessApi.Get(creator.businessGuid)) ?: throw unprocessable(
        BusinessNotFound()
      )
    val businessName = business.name ?: throw BusinessIsNotOnboardedWithHighbeamYet()
    val bankAccount =
      bankAccountClient.request(BankAccountApi.Get(creator.bankAccountGuid)) ?: throw unprocessable(
        BankAccountNotFound()
      )

    val internationalWireTransferMethod =
      payeePaymentMethodService.getByPayeeGuid(creator.payeeGuid).singleNullOrThrow {
        it.paymentMethod == TransferRep.PaymentMethod.International
      } ?: throw unprocessable(InternationalWirePaymentMethodNotFound())

    if (internationalWireTransferMethod.currency != creator.receivedCurrency) {
      throw InternationalWirePaymentMethodCurrencyMismatch()
    }

    if (creator.receivedCurrency != "USD") {
      val buyRate = creator.buyRate ?: throw InternationalWireQuoteNotFound()
      val passedReceivedAmount = creator.receivedAmount.toDollarsAndCents(
        Currency.getInstance(creator.receivedCurrency)
      )
      val expectedReceivedAmount =
        Money.fromDollarsAndCents(creator.amount.toDollarsAndCents() * buyRate).toDollarsAndCents(
          Currency.getInstance(creator.receivedCurrency)
        )
      if (abs(expectedReceivedAmount - passedReceivedAmount) > CURRENCY_RATE_EPSILON) {
        throw InternationalWireQuoteMismatch()
      }
    }

    val generalPaymentMetadataGuid = creator.generalPaymentMetadataGuid ?: uuidGenerator.generate()

    paymentService.checkMinimumBalanceRequirement(
      bankAccount = bankAccount,
      paymentAmount = creator.amountWithFees(),
      unitPaymentType = UnitPaymentRep.UnitPaymentType.Wire,
      direction = UnitPaymentRep.Direction.Credit,
    )

    val airwallexBankAccount = airwallexBankAccountClient.request(
      AirwallexBankAccountApi.GetByBusinessGuid(creator.businessGuid)
    ) ?: throw unprocessable(AirwallexBankAccountNotFound())

    logger.info {
      "Creating Airwallex payment for business: $businessName, amount: ${creator.amount}, " +
              "onBehalfOf: ${airwallexBankAccount.airwallexAccountId}"
    }

    val (unitPaymentId, paymentType) = fundAirwallex(
      creator = creator,
      bankAccount = bankAccount,
      airwallexBankAccount = airwallexBankAccount,
      businessName = businessName,
      paymentGatewayPaymentGuid = creator.paymentGuid,
      generalPaymentMetadataGuid = generalPaymentMetadataGuid,
    )

    var conversion: FxQuoteRep? = null
    var externalPayment: AirwallexTransferRep? = null

    @Suppress("TooGenericExceptionCaught") try {
      conversion = if (creator.receivedCurrency != "USD") {
        createQuote(
          creator = creator,
          transactionId = generalPaymentMetadataGuid,
          airwallexAccountId = airwallexBankAccount.airwallexAccountId,
        )
      } else {
        null
      }

      // Calculate payment fee (this will throw if negative)
      val paymentFee = calculatePaymentFee(creator, conversion)

      externalPayment = createAirwallexTransfer(
        internationalWireTransferMethod = internationalWireTransferMethod,
        creator = creator,
        conversion = conversion,
        paymentFee = paymentFee,
        airwallexAccountId = airwallexBankAccount.airwallexAccountId,
      )

      // TODO: Payment metadata

      return AirwallexPaymentRep(
        amount = creator.receivedAmount,
        createdAt = ZonedDateTime.now(clock),
        currency = creator.receivedCurrency,
        externalPaymentId = externalPayment.id,
        paymentFee = paymentFee,
        paymentFeeCurrency = "USD",
        generalPaymentMetadataGuid = generalPaymentMetadataGuid,
        reason = creator.reason,
        status = "ReadyToSend",
        transferMethod = creator.transferMethod,
      )
    } catch (e: Exception) {
      logger.error("Failed to create international wire payment: $e")
      cancelInProgressAirwallexWire(
        unitPaymentId = unitPaymentId,
        paymentType = paymentType,
      )
      throw e
    }
  }

  private suspend fun createAirwallexTransfer(
    internationalWireTransferMethod: PayeePaymentMethodModel,
    creator: AirwallexPaymentRep.Creator,
    conversion: FxQuoteRep?,
    paymentFee: Money,
    airwallexAccountId: String,
  ): AirwallexTransferRep {
    logger.info { "Creating Airwallex transfer for payee: ${creator.payeeGuid}" }

    val transferCreator = AirwallexTransferRep.Creator(
      applicableFeeOptions = listOf(
        AirwallexTransferRep.ApplicationFeeOption(
          sourceType = "TRANSFER",
          type = "FIXED",
          amount = paymentFee.rawCents,
          currency = "USD",
        )
      ),
      beneficiary = null,
      beneficiaryId = internationalWireTransferMethod.airwallexBeneficiaryId,
      clientData = null,
      feePaidBy = "PAYER",
      metadata = null,
      payer = null,
      payerId = null,
      quoteId = conversion?.quoteId,
      reason = creator.reason,
      reference = creator.description.take(140),
      remarks = creator.description,
      requestId = creator.idempotencyKey.toString(),
      sourceAmount = null,
      sourceCurrency = "USD",
      swiftChargeOption = "PAYER",
      transferAmount = creator.receivedAmount.toDollarsAndCents(
        Currency.getInstance(creator.receivedCurrency)
      ).toBigDecimal(),
      transferCurrency = creator.receivedCurrency,
      transferDate = null,
      transferMethod = creator.transferMethod.value,
    )

    return airwallexClient.onBehalfOf(airwallexAccountId) {
      transfer.create(transferCreator)
    }
  }

  private suspend fun fundAirwallex(
    creator: AirwallexPaymentRep.Creator,
    bankAccount: BankAccountRep.Complete,
    airwallexBankAccount: AirwallexBankAccountRep,
    businessName: String,
    generalPaymentMetadataGuid: UUID,
    paymentGatewayPaymentGuid: UUID?
  ): Pair<String, UnitPaymentRep.UnitPaymentType> {
    val currentLimitAllowsForAchPayment =
      creator.amount <= INTERNATIONAL_WIRE_FUNDING_WIRE_REQUIREMENT

    if (currentLimitAllowsForAchPayment) {
      val unitACHPayment = fundAirwallexAccountWithAch(
        bankAccount = bankAccount,
        creator = creator,
        airwallexBankAccount = airwallexBankAccount,
        businessName = businessName,
        generalPaymentMetadataGuid = generalPaymentMetadataGuid,
        paymentGatewayPaymentGuid = paymentGatewayPaymentGuid
      )
      if (unitACHPayment.status in UnitCoAchPaymentRep.successfulStatuses()) {
        return Pair(unitACHPayment.id, UnitPaymentRep.UnitPaymentType.Ach)
      } else {
        logger.warn {
          "Airwallex ACH funding failed for : $businessName for ${creator.amount}. " +
                  "Falling back to Wire"
        }
      }
    }

    // We need to change the idempotency key. If we don't, Unit will retry the ACH payment.
    // We use a UUID deterministically-derived from the existing idempotency key.
    val newIdempotencyKey =
      UUID.nameUUIDFromBytes(creator.idempotencyKey.asByteArray() + "wire".toByteArray())
    val retryCreation = creator.copy(idempotencyKey = newIdempotencyKey)
    val unitWirePayment = fundAirwallexAccountWithWire(
      bankAccount = bankAccount,
      creator = retryCreation,
      internationalBankAccount = airwallexBankAccount,
      businessName = businessName,
      generalPaymentMetadataGuid = generalPaymentMetadataGuid,
      paymentGatewayPaymentGuid = paymentGatewayPaymentGuid
    )
    if (!UnitCoWirePaymentRep.successfulStatuses().contains(unitWirePayment.status)) {
      throw InternationalWireFundingFailed()
    }
    return Pair(unitWirePayment.id, UnitPaymentRep.UnitPaymentType.Wire)
  }

  private suspend fun fundAirwallexAccountWithAch(
    bankAccount: BankAccountRep.Complete,
    creator: AirwallexPaymentRep.Creator,
    airwallexBankAccount: AirwallexBankAccountRep,
    businessName: String,
    generalPaymentMetadataGuid: UUID,
    paymentGatewayPaymentGuid: UUID?
  ): UnitCoAchPaymentRep.Complete {
    val counterparty = AchPaymentRep.InlineCounterparty(
      routingNumber = ProtectedString(airwallexBankAccount.achRoutingNumber),
      accountNumber = ProtectedString(airwallexBankAccount.achAccountNumber),
      accountType = AchPaymentRep.AccountType.Checking,
      name = businessName
    )
    val unitCoAchCreation = creator.toUnitCoAchCreation(
      bankAccount = bankAccount,
      inlineCounterparty = counterparty,
      payeeGuid = creator.payeeGuid,
      paymentGuid = paymentGatewayPaymentGuid,
      generalPaymentMetadataGuid = generalPaymentMetadataGuid,
      // We only want to add the payeeEmail if the user has toggled it for this payment
      payeeEmail = if (creator.payeeEmailToggle) creator.payeeEmail else null,
      sameDay = true
    )

    logger.info { "Funding via ACH payment in unitCo for international wire : $unitCoAchCreation." }
    return unitCoClient.payment.createAch(unitCoAchCreation)
  }

  private suspend fun fundAirwallexAccountWithWire(
    bankAccount: BankAccountRep.Complete,
    creator: AirwallexPaymentRep.Creator,
    internationalBankAccount: AirwallexBankAccountRep,
    businessName: String,
    generalPaymentMetadataGuid: UUID,
    paymentGatewayPaymentGuid: UUID?
  ): UnitCoWirePaymentRep.Complete {
    val inlineCounterparty = WirePaymentRep.InlineCounterparty(
      routingNumber = ProtectedString(internationalBankAccount.wireRoutingNumber),
      accountNumber = ProtectedString(internationalBankAccount.wireAccountNumber),
      name = businessName,
      address = INTERNATIONAL_WIRE_FUNDING_WIRE_ADDRESS
    )

    val unitCoWireCreation = creator.toUnitCoWireCreation(
      bankAccount = bankAccount,
      inlineCounterparty = inlineCounterparty,
      payeeGuid = creator.payeeGuid,
      paymentGuid = paymentGatewayPaymentGuid,
      payeeEmail = creator.payeeEmail,
      generalPaymentMetadataGuid = generalPaymentMetadataGuid
    )

    logger.info {
      "Funding via wire payment in unitCo for international wire : $unitCoWireCreation."
    }
    return unitCoClient.payment.createWire(unitCoWireCreation)
  }

  private suspend fun createQuote(
    creator: AirwallexPaymentRep.Creator,
    transactionId: UUID,
    airwallexAccountId: String,
  ): FxQuoteRep {
    logger.info {
      "Creating Airwallex FX quote to sell USD for ${creator.receivedCurrency}, " +
              "amount: ${creator.amount}, transactionId: $transactionId"
    }

    // Create a guaranteed FX quote valid for 15 minutes
    // We sell USD to buy the target currency
    val quoteCreator = FxQuoteRep.Creator(
      applicationFees = null,
      buyAmount = creator.receivedAmount.toDollarsAndCents(
        Currency.getInstance(creator.receivedCurrency)
      ).toBigDecimal(),
      buyCurrency = creator.receivedCurrency,
      conversionDate = null, // Use current date
      sellAmount = null, // Let Airwallex calculate the sell amount based on buy amount
      sellCurrency = "USD",
      validity = "MIN_15" // 15 minutes validity
    )

    return airwallexClient.onBehalfOf(airwallexAccountId) {
      fxQuote.create(quoteCreator)
    }
  }

  private fun calculatePaymentFee(
    creator: AirwallexPaymentRep.Creator,
    quote: FxQuoteRep?,
  ): Money {
    return if (creator.receivedCurrency == "USD") {
      // For USD to USD transfers, use fixed $20 fee
      INTERNATIONAL_WIRE_PRIORITY_FEE
    } else {
      // For currency conversions, calculate the spread fee
      // Fee = creator.amount - (receivedAmount * quoteRate)
      requireNotNull(quote) { "Quote is required for non-USD transfers" }

      val expectedUsdAmount = creator.receivedAmount.toDollarsAndCents(
        Currency.getInstance(creator.receivedCurrency)
      ).toBigDecimal() * quote.clientRate

      val actualUsdAmount =
        creator.amount.toDollarsAndCents(Currency.getInstance("USD")).toBigDecimal()

      val feeInDollars = actualUsdAmount - expectedUsdAmount

      logger.info {
        "Calculated Airwallex fee: actualUSD=$actualUsdAmount, " +
                "expectedUSD=$expectedUsdAmount, fee=$feeInDollars, " +
                "receivedAmount=${creator.receivedAmount}, rate=${quote.clientRate}"
      }

      // Check if fee is negative (unfavorable spread)
      if (feeInDollars < BigDecimal.ZERO) {
        throw AirwallexNegativeFeeException(
          calculatedFee = feeInDollars.toDouble(),
        )
      }

      // Convert back to Money (cents)
      Money.fromDollars(feeInDollars)
    }
  }

  private suspend fun cancelInProgressAirwallexWire(
    unitPaymentId: String,
    paymentType: UnitPaymentRep.UnitPaymentType,
  ) {
    @Suppress("TooGenericExceptionCaught") try {
      if (paymentType == UnitPaymentRep.UnitPaymentType.Ach) {
        logger.info("Cancelling ACH payment: $unitPaymentId")
        unitCoClient.payment.cancelAch(unitPaymentId)
      } else {
        logger.info("Cannot cancel wire payment: $unitPaymentId")
        throw InternationalWireFundingCancelFailed()
      }
    } catch (e: Exception) {
      logger.error(e) { "Failed to cancel international payment for unit payment $unitPaymentId" }
      throw e
    }
  }

  override suspend fun cancelAirwallexPayment(
    businessGuid: UUID,
    generalPaymentMetadataGuid: UUID,
  ) {
    // TODO Cancel airwallex
  }

  override suspend fun getAirwallexQuote(currency: String): InternationalQuoteRep.Complete {
    if (!ENABLED_BUY_CURRENCIES.contains(currency)) {
      throw unprocessable(InternationalQuoteNotFound())
    }

    val fxRate = airwallexClient.fxQuote.getRate(
      buyCurrency = currency,
      sellCurrency = "USD",
      sellAmount = Money.fromDollars(100),
    )

    val currencyRateWithFee =
      (fxRate.rate * (CURRENCY_RATE.toBigDecimal())).setScale(5, RoundingMode.HALF_EVEN)
    val inverse = BigDecimal.ONE.divide(currencyRateWithFee, 16, RoundingMode.HALF_EVEN)

    return InternationalQuoteRep.Complete(
      rate = currencyRateWithFee.toDouble(),
      inverse = inverse.toDouble(),
    )
  }
}
