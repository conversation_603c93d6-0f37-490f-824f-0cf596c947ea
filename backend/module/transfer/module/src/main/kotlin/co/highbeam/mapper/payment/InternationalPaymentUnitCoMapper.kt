package co.highbeam.mapper.payment

import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.TransferRep
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.payment.AchPaymentRep
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_PRIORITY_FEE
import co.highbeam.rep.payment.InternationalPaymentRep
import co.highbeam.rep.payment.WirePaymentRep
import co.unit.rep.AddressRep
import co.unit.rep.UnitCoAchPaymentRep
import co.unit.rep.UnitCoPayment
import co.unit.rep.UnitCoWirePaymentRep
import com.currencycloud.client.model.Payment
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID

// UnitCo has two fields for ACH notes - Description [Length 10] & Addenda [Length 80]
// And one field for wire notes - Description [Length 50]
private const val UNIT_CO_ACH_DESCRIPTION_LENGTH: Int = 10
private const val UNIT_CO_ACH_ADDENDA_LENGTH: Int = 80
private const val UNIT_CO_WIRE_DESCRIPTION_LENGTH: Int = 50

object InternationalPaymentUnitCoMapper {
  fun InternationalPaymentRep.Creator.toUnitCoAchCreation(
    bankAccount: BankAccountRep.Complete,
    inlineCounterparty: AchPaymentRep.InlineCounterparty,
    generalPaymentMetadataGuid: UUID?,
    payeeGuid: UUID,
    paymentGuid: UUID?,
    paymentMetadataGuid: UUID,
    payeeEmail: String?,
    sameDay: Boolean = false,
  ): UnitCoAchPaymentRep.Creator = UnitCoAchPaymentRep.Creator(
    amount = this.amountWithFees(),
    direction = MoneyDirection.Credit,
    counterparty = inlineCounterparty.toInlineCounterParty(),
    description = trimToUnitCoAchDescription(this.description),
    addenda = trimToUnitCoAchAddenda(this.reason),
    generalPaymentMetadataGuid = generalPaymentMetadataGuid,
    idempotencyKey = this.idempotencyKey,
    payeeGuid = payeeGuid,
    fromAccountType = bankAccount.type,
    fromAccountId = bankAccount.unitCoDepositAccountId,
    paymentGuid = paymentGuid,
    paymentMetadataGuid = paymentMetadataGuid,
    paymentType = UnitCoPayment.PaymentType.InternationalWire,
    payeeEmail = payeeEmail,
    sameDay = sameDay,
    tags = this.tags,
  )

  fun InternationalPaymentRep.Creator.toUnitCoWireCreation(
    bankAccount: BankAccountRep.Complete,
    inlineCounterparty: WirePaymentRep.InlineCounterparty,
    payeeGuid: UUID,
    paymentGuid: UUID?,
    paymentMetadataGuid: UUID,
    payeeEmail: String?,
    generalPaymentMetadataGuid: UUID?,
  ): UnitCoWirePaymentRep.Creator = UnitCoWirePaymentRep.Creator(
    amount = this.amountWithFees(),
    inlineCounterparty = inlineCounterparty.toInlineCounterParty(),
    description = trimToUnitCoWireDescription(this.description + " | " + this.reason),
    idempotencyKey = this.idempotencyKey,
    payeeGuid = payeeGuid,
    fromAccountType = bankAccount.type,
    fromAccountId = bankAccount.unitCoDepositAccountId,
    paymentGuid = paymentGuid,
    paymentMetadataGuid = paymentMetadataGuid,
    paymentType = UnitCoPayment.PaymentType.InternationalWire,
    payeeEmail = payeeEmail,
    generalPaymentMetadataGuid = generalPaymentMetadataGuid,
    tags = this.tags,
  )

  fun InternationalPaymentRep.Creator.amountWithFees(): Money {
    val fee = if (this.receivedCurrency == "USD") {
      INTERNATIONAL_WIRE_PRIORITY_FEE
    } else {
      Money(0)
    }

    return this.amount + fee
  }

  fun trimToUnitCoAchDescription(fullDescription: String) =
    fullDescription.take(UNIT_CO_ACH_DESCRIPTION_LENGTH).ifBlank { "Payment" }

  fun trimToUnitCoAchAddenda(fullAddenda: String) =
    fullAddenda.take(UNIT_CO_ACH_ADDENDA_LENGTH).ifBlank { "International wire" }

  fun trimToUnitCoWireDescription(fullDescription: String) =
    fullDescription.take(UNIT_CO_WIRE_DESCRIPTION_LENGTH).ifBlank { "International wire" }

  fun AchPaymentRep.InlineCounterparty.toInlineCounterParty() =
    UnitCoAchPaymentRep.InlineCounterparty(
      routingNumber = this.routingNumber.value,
      accountNumber = this.accountNumber.value,
      name = this.name,
      accountType = UnitCoAchPaymentRep.AccountType.valueOf(this.accountType.name),
    )

  fun WirePaymentRep.InlineCounterparty.toInlineCounterParty() =
    UnitCoWirePaymentRep.InlineCounterparty(
      routingNumber = this.routingNumber.value,
      accountNumber = this.accountNumber.value,
      name = this.name,
      address = this.address.toUnitCoAddress(),
    )

  private fun TransferRep.Address.toUnitCoAddress() =
    AddressRep(
      street = this.addressLine1,
      street2 = this.addressLine2,
      city = this.city,
      state = this.state,
      postalCode = this.zipCode,
      country = this.country,
    )

  fun Payment.toCompleteRep(paymentMetadataGuid: UUID): InternationalPaymentRep.Complete {
    val feeAmount = this.feeAmount?.let { Money.fromString(it.toPlainString()) }

    return InternationalPaymentRep.Complete(
      amount = Money.fromString(this.amount.toPlainString()),
      createdAt = ZonedDateTime.ofInstant(this.createdAt.toInstant(), ZoneOffset.UTC),
      externalPaymentId = UUID.fromString(this.id),
      currency = this.currency,
      paymentFee = feeAmount,
      paymentFeeCurrency = this.feeCurrency,
      paymentMetadataGuid = paymentMetadataGuid,
      paymentType = InternationalPaymentRep.PaymentType.valueOf(this.paymentType),
      reason = this.reason,
      status = this.status
    )
  }
}
