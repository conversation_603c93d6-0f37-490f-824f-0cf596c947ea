package co.highbeam.service.payment

import co.highbeam.rep.payment.AirwallexPaymentRep
import co.highbeam.rep.payment.InternationalQuoteRep
import com.google.inject.ImplementedBy
import java.util.UUID

const val AIRWALLEX_SLACK_WEBHOOK_PATH = "AIRWALLEX_SLACK_WEBHOOK_PATH"

@ImplementedBy(AirwallexPaymentServiceImpl::class)
internal interface AirwallexPaymentService {
  suspend fun createAirwallexPayment(creator: AirwallexPaymentRep.Creator): AirwallexPaymentRep

  suspend fun cancelAirwallexPayment(businessGuid: UUID, generalPaymentMetadataGuid: UUID)

  suspend fun getAirwallexQuote(currency: String): InternationalQuoteRep.Complete
}
