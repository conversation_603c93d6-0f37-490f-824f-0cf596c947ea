package co.highbeam.testing

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.client.backendV2.insights.InsightsSyncTaskClient
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessAddress.BusinessAddressClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.currencyCloudWebhook.CurrencyCloudWebhookClient
import co.highbeam.client.institution.InstitutionClient
import co.highbeam.client.internationalBankAccount.InternationalBankAccountClient
import co.highbeam.client.payee.PayeeClient
import co.highbeam.client.payment.PaymentClient
import co.highbeam.client.paymentDetails.PaymentDetailsClient
import co.highbeam.client.paymentMetadata.GeneralPaymentMetadataClient
import co.highbeam.client.user.UserClient
import co.highbeam.config.ConfigLoader
import co.highbeam.config.TransferFeatureTestConfig
import co.highbeam.email.EmailService
import co.highbeam.event.FakeEventFeature
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.feature.transfer.TransferFeature
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import co.unit.client.UnitCoClient
import com.airwallex.client.AirwallexClient
import com.currencycloud.client.CurrencyCloudClient
import com.slack.client.SlackMessageClient
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import java.time.LocalDate
import java.util.UUID
import co.highbeam.client.paymentV2.PaymentClient as PaymentV2Client

internal val TEST_CURRENCY_CLOUD_WEBHOOK_TOKEN = ProtectedString("testccwebhooktoken")
const val TEST_SLACK_WEBHOOK_PATH = "testslackwebhookpath"

@ExtendWith(TransferFeatureIntegrationTest.Extension::class)
internal abstract class TransferFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  private object MockFeature : AbstractMockFeature() {
    override fun bind() {
      mock(InsightsSyncTaskClient::class)
      mock(AirwallexClient::class)
      mock(BankAccountClient::class)
      mock(BusinessClient::class)
      mock(BusinessAddressClient::class)
      mock(BusinessMemberClient::class)
      mock(CurrencyCloudClient::class)
      mock(EmailService::class, mockk(relaxed = true))
      mock(FeatureFlagService::class, FakeFeatureFlagService())
      mock(InternationalBankAccountClient::class)
      mock(PaymentV2Client::class)
      mock(SlackMessageClient::class)
      mock(UnitCoClient::class)
      mock(UserClient::class)
    }
  }

  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val config: TransferFeatureTestConfig = ConfigLoader.load("test")
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "transfer",
      )
      val eventFeature = FakeEventFeature
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<TransferFeatureTestConfig>(config) {
          override val features = setOf(
            TestRestFeature(),
            sqlFeature,
            eventFeature,
            MockFeature,
            TransferFeature(
              TEST_CURRENCY_CLOUD_WEBHOOK_TOKEN,
              TEST_SLACK_WEBHOOK_PATH,
              TEST_SLACK_WEBHOOK_PATH,
            ),
          )
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      sqlFeature.truncateSchema(context[Server::class.java].injector)
      eventFeature.reset()
    }

    override fun stop() {
      sharedState.stop()
    }
  }

  val generalPaymentMetadataClient: GeneralPaymentMetadataClient by lazy {
    GeneralPaymentMetadataClient(httpClient)
  }

  val institutionClient: InstitutionClient by lazy {
    InstitutionClient(httpClient)
  }

  val payeeClient: PayeeClient by lazy {
    PayeeClient(httpClient)
  }

  val paymentClient: PaymentClient by lazy {
    PaymentClient(httpClient)
  }

  val paymentDetailsClient: PaymentDetailsClient by lazy {
    PaymentDetailsClient(httpClient)
  }

  val webhookClient: CurrencyCloudWebhookClient by lazy {
    CurrencyCloudWebhookClient(httpClient)
  }

  protected fun mockBusiness(
    businessGuid: UUID,
    name: String?,
    ownerUserGuid: UUID = UUID.randomUUID(),
  ) {
    mockBusiness(
      businessGuid = businessGuid,
      business = mockk {
        every { <EMAIL> } returns businessGuid
        every { <EMAIL> } returns name
        every { <EMAIL> } returns name
        every { <EMAIL> } returns ownerUserGuid
      },
    )
  }

  private fun mockBusiness(businessGuid: UUID, business: BusinessRep.Complete?) {
    coEvery { get<BusinessClient>().request(BusinessApi.Get(businessGuid)) } returns business
  }

  protected fun mockGetAllAdmins(businessGuid: UUID) {
    coEvery {
      get<BusinessMemberClient>().request(
        BusinessMemberApi.GetAdminsByBusiness(businessGuid)
      )
    } returns listOf(
      BusinessMemberRep.Complete(
        guid = UUID.randomUUID(),
        businessGuid = businessGuid,
        addedByUserGuid = null,
        userGuid = UUID.randomUUID(),
        emailAddress = "<EMAIL>",
        firstName = "Bob",
        lastName = "the Builder",
        phoneNumber = "************",
        dateOfBirth = LocalDate.of(2000, 1, 1),
        isOnboarded = true,
      ),
      BusinessMemberRep.Complete(
        guid = UUID.randomUUID(),
        businessGuid = businessGuid,
        addedByUserGuid = null,
        userGuid = UUID.randomUUID(),
        emailAddress = "<EMAIL>",
        firstName = "John",
        lastName = "the Builder",
        phoneNumber = "************",
        dateOfBirth = LocalDate.of(2000, 1, 1),
        isOnboarded = true,
      )
    )
  }
}
