plugins {
  id("highbeam-jvm")
}

dependencies {
  api(project(":common:rest-feature"))
  implementation(project(":common:airwallex"))
  implementation(project(":common:backend-v2"))
  implementation(project(":common:email"))
  implementation(project(":common:event"))
  implementation(project(":common:hashing"))
  implementation(project(":common:job-runner"))
  implementation(project(":common:slack"))
  implementation(project(":common:sql"))
  implementation(project(":common:unit-co"))
  implementation(project(":module:business:business:client"))
  implementation(project(":module:business:details:client"))
  implementation(project(":module:highbeam:client"))
  implementation(project(":module:payment:gateway:client"))
  implementation(project(":module:job:client"))
  implementation(project(":module:user:client"))

  api(project(":module:transfer:interface"))

  testImplementation(project(":common:event:testing"))
  testImplementation(project(":common:feature:feature-flags-testing"))
  testImplementation(project(":common:integration-testing"))
  testImplementation(project(":common:job-runner:testing"))
  testImplementation(project(":common:rest-feature:testing"))
  testImplementation(project(":common:sql:testing"))
  testImplementation(project(":db:highbeam"))
  testImplementation(project(":module:transfer:client"))
}
