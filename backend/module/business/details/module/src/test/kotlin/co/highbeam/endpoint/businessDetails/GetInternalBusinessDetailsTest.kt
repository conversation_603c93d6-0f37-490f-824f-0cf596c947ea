package co.highbeam.endpoint.businessDetails

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessDetails.BusinessDetailsApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessDetails.InternalBusinessDetailsRep
import co.highbeam.server.Server
import co.highbeam.testing.BusinessDetailsFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.client.UnitCoClient
import co.unit.rep.ApplicationRep
import co.unit.rep.BusinessContactRep
import co.unit.rep.CustomerRep
import co.unit.rep.FullNameRep
import co.unit.rep.PhoneRep
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.test.assertFailsWith

internal class GetInternalBusinessDetailsTest(
  server: Server<*>,
) : BusinessDetailsFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()

  @Test
  fun `business does not exist`() = integrationTest {
    mockBusiness(null)
    assertHighbeamException {
      makeInternalRequest(BusinessDetailsApi.GetInternal(businessGuid))
    }.isHighbeamException(unprocessable(BusinessNotFound()))
  }

  @Test
  fun `unit customer does not exist`() = integrationTest {
    mockBusiness(
      business(
        customerId = "unit-customer-123",
        name = "FAMOUS JOE'S PIZZA, INC",
        dba = "Joe's Pizza",
      ),
    )
    mockUnitCustomer(id = "unit-customer-123", null)
    assertFailsWith<HighbeamHttpClientException> {
      makeInternalRequest(BusinessDetailsApi.GetInternal(businessGuid))
    }.let {
      assertThat(it.statusCode).isEqualTo(HttpStatusCode.InternalServerError)
    }
  }

  @Test
  fun `happy path`() = integrationTest {
    mockBusiness(
      business(
        customerId = "unit-customer-123",
        name = "FAMOUS JOE'S PIZZA, INC",
        dba = "Joe's Pizza",
      ),
    )
    mockUnitCustomer(
      id = "unit-customer-123",
      unitCustomer(
        name = "FAMOUS JOE'S PIZZA, INC",
        dba = "Joe's Pizza",
        phone = PhoneRep("1", "6465594878"),
      ),
    )
    mockUnitApplication(
      businessGuid = businessGuid,
      unitApplication(
        website = "joespizzanyc.com",
        ein = "12-3456789",
        stateOfIncorporation = "NY",
        contact = BusinessContactRep(
          fullName = FullNameRep(
            first = "Joe",
            last = "Smith",
          ),
          email = "<EMAIL>",
          phone = PhoneRep("1", "6465594878"),
        ),
      ),
    )
    val expected = InternalBusinessDetailsRep(
      name = "FAMOUS JOE'S PIZZA, INC",
      dba = "Joe's Pizza",
      website = "joespizzanyc.com",
      phoneNumber = "*************",
      ein = "12-3456789",
      incorporationState = "NY",
      associatedPerson = "Joe Smith",
    )
    assertThat(makeInternalRequest(BusinessDetailsApi.GetInternal(businessGuid)))
      .isEqualTo(expected)
  }

  private suspend fun makeInternalRequest(
    endpoint: BusinessDetailsApi.GetInternal,
  ): InternalBusinessDetailsRep? =
    httpClient.request(endpoint).readValue()

  private fun mockBusiness(business: BusinessRep.Complete?) {
    coEvery {
      get<BusinessClient>().request(BusinessApi.Get(businessGuid))
    } returns business
  }

  private fun business(
    customerId: String,
    name: String,
    dba: String?,
  ): BusinessRep.Complete =
    mockk {
      every { <EMAIL> } returns customerId
      every { <EMAIL> } returns name
      every { <EMAIL> } returns dba
    }

  private fun mockUnitCustomer(id: String, customer: CustomerRep.Complete?) {
    coEvery {
      get<UnitCoClient>().customer.get(id)
    } returns customer
  }

  private fun unitCustomer(
    name: String,
    dba: String?,
    phone: PhoneRep,
  ): CustomerRep.Complete.Business =
    mockk {
      every { <EMAIL> } returns name
      every { <EMAIL> } returns dba
      every { <EMAIL> } returns phone
    }

  private fun mockUnitApplication(businessGuid: UUID, application: ApplicationRep.Complete?) {
    coEvery {
      get<UnitCoClient>().application.getByBusiness(businessGuid)
    } returns listOfNotNull(application)
  }

  private fun unitApplication(
    website: String?,
    ein: String,
    stateOfIncorporation: String,
    contact: BusinessContactRep,
  ): ApplicationRep.Complete.Business =
    mockk {
      every { <EMAIL> } returns ApplicationRep.Status.Approved
      every { <EMAIL> } returns website
      every { <EMAIL> } returns ein
      every { <EMAIL> } returns stateOfIncorporation
      every { <EMAIL> } returns contact
    }
}
