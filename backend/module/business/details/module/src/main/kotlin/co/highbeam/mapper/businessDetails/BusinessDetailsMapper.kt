package co.highbeam.mapper.businessDetails

import co.highbeam.model.businessDetails.BusinessDetailsModel
import co.highbeam.model.businessDetails.InternalBusinessDetailsModel
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessDetails.BusinessDetailsRep
import co.highbeam.rep.businessDetails.InternalBusinessDetailsRep
import co.unit.rep.ApplicationRep
import co.unit.rep.CustomerRep
import com.google.inject.Inject
import mu.KotlinLogging

internal class BusinessDetailsMapper @Inject constructor() {
  private val logger = KotlinLogging.logger {}
  fun map(details: BusinessDetailsModel): BusinessDetailsRep =
    BusinessDetailsRep(
      name = details.name,
      dba = details.dba,
      website = details.website,
      phoneNumber = details.phoneNumber,
    )

  fun map(updater: BusinessDetailsRep.Updater): BusinessDetailsModel.Updater =
    BusinessDetailsModel.Updater(
      dba = updater.dba,
      phoneNumber = updater.phoneNumber,
    )

  fun mapInternal(details: InternalBusinessDetailsModel): InternalBusinessDetailsRep =
    InternalBusinessDetailsRep(
      name = details.name,
      dba = details.dba,
      website = details.website,
      phoneNumber = details.phoneNumber,
      ein = details.ein,
      incorporationState = details.incorporationState,
      associatedPerson = details.associatedPerson,
    )

  fun mapInternal(
    business: BusinessRep.Complete,
    customer: CustomerRep.Complete.Business,
    application: ApplicationRep.Complete.Business,
  ): InternalBusinessDetailsModel {
    val name = checkNotNull(business.name) {
      "Once the Unit customer ID is set, the name should exist."
    }
    if (customer.name != name) {
      logger.warn { "Business $business has different name locally and in Unit." }
    }
    if (customer.dba != business.dba) {
      logger.warn { "Business $business has different dba locally and in Unit." }
    }
    val fullName = application.contact.fullName
    return InternalBusinessDetailsModel(
      name = customer.name,
      dba = customer.dba,
      website = application.website,
      phoneNumber = customer.phone.toE164(),
      ein = application.ein,
      incorporationState = application.stateOfIncorporation,
      associatedPerson = fullName?.run { "$first $last" }.orEmpty(),
    )
  }
}
