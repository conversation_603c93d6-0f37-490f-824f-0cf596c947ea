package co.highbeam.feature.business

import co.highbeam.endpoint.businessAddress.GetBusinessAddress
import co.highbeam.endpoint.businessAddress.SetUnitBusinessAddress
import co.highbeam.endpoint.businessDetails.GetBusinessDetails
import co.highbeam.endpoint.businessDetails.GetInternalBusinessDetails
import co.highbeam.endpoint.businessDetails.UpdateBusinessDetails
import co.highbeam.feature.Feature
import co.highbeam.listener.businessDetails.UnitCoWebhookEventListener

class BusinessDetailsFeature : Feature() {
  override fun bind() {
    bindApiEndpoints()
    bindListeners()
  }

  private fun bindApiEndpoints() {
    bind(GetBusinessAddress::class.java).asEagerSingleton()
    bind(SetUnitBusinessAddress::class.java).asEagerSingleton()

    bind(GetBusinessDetails::class.java).asEagerSingleton()
    bind(GetInternalBusinessDetails::class.java).asEagerSingleton()
    bind(UpdateBusinessDetails::class.java).asEagerSingleton()
  }

  private fun bindListeners() {
    bind(UnitCoWebhookEventListener::class.java)
  }
}
