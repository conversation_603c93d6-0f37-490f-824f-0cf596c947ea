package co.highbeam.api.businessDetails

import co.highbeam.rep.businessDetails.BusinessDetailsRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object BusinessDetailsApi {
  data class Get(
    val businessGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/businesses/$businessGuid/details",
  )

  data class GetInternal(
    val businessGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/businesses/$businessGuid/internal-details",
  )

  data class Update(
    val businessGuid: UUID,
    val updater: BusinessDetailsRep.Updater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/businesses/$businessGuid/details",
    body = updater,
  )
}
