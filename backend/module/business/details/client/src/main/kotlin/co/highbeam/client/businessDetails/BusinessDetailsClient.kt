package co.highbeam.client.businessDetails

import co.highbeam.api.businessDetails.BusinessDetailsApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.business.BUSINESS_DETAILS_FEATURE
import co.highbeam.rep.businessDetails.BusinessDetailsRep
import co.highbeam.rep.businessDetails.InternalBusinessDetailsRep
import com.google.inject.Inject
import com.google.inject.name.Named

class BusinessDetailsClient @Inject constructor(
  @Named(BUSINESS_DETAILS_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: BusinessDetailsApi.Get): BusinessDetailsRep? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessDetailsApi.GetInternal): InternalBusinessDetailsRep? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessDetailsApi.Update): BusinessDetailsRep =
    httpClient.request(endpoint).readValue()
}
