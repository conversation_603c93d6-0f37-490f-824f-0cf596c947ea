package co.highbeam.bankAccount

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector.ChargeCardRepayment as Config

internal class ChargeCardRepaymentBankAccount private constructor(
  private val capitalAccountClient: CapitalAccountClient,
  private val bankAccountClient: BankAccountClient,
  private val capitalAccountGuid: UUID,
) : BankAccountSelection {
  override suspend fun get(
    businessGuid: UUID,
  ): BankAccount? {
    val capitalAccount = capitalAccountClient(
      CapitalAccountApi.Get(
        businessGuid = businessGuid,
        guid = capitalAccountGuid,
      )
    ) ?: return null

    val bankAccountRep = capitalAccount.details.repayment.bankAccountGuid?.let {
      bankAccountClient.request(
        BankAccountApi.Get(accountGuid = it)
      )
    } ?: bankAccountClient.request(
      BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid)
    )

    if (bankAccountRep == null) {
      return null
    }

    return BankAccount(
      guid = bankAccountRep.guid,
      accountNumber = bankAccountRep.accountNumber,
      routingNumber = bankAccountRep.routingNumber,
      availableBalance = bankAccountRep.availableBalance,
      name = bankAccountRep.name,
      unitCoDepositAccountId = bankAccountRep.unitCoDepositAccountId,
    )
  }

  class Builder @Inject constructor(
    private val capitalAccountClient: CapitalAccountClient,
    private val bankAccountClient: BankAccountClient,
  ) : BankAccountSelection.Builder<Config>() {
    override fun build(config: Config): BankAccountSelection {
      return ChargeCardRepaymentBankAccount(
        capitalAccountClient = capitalAccountClient,
        bankAccountClient = bankAccountClient,
        capitalAccountGuid = config.capitalAccountGuid,
      )
    }
  }
}
