plugins {
  id("highbeam-jvm")
}

dependencies {
  api(project(":common:auth0"))
  api(project(":common:client"))
  api(project(":common:config"))
  api(project(":common:rest-feature"))
  api(project(":module:capital:credit:interface"))
  implementation(project(":common:email"))
  implementation(project(":common:event"))
  implementation(project(":common:google-cloud-storage"))
  implementation(project(":common:job-runner"))
  implementation(project(":common:slack"))
  implementation(project(":common:sql"))
  implementation(project(":common:task"))
  implementation(project(":common:taktile"))
  implementation(project(":common:unit-co"))
  implementation(project(":module:business:business:client"))
  implementation(project(":module:business:details:client"))
  implementation(project(":module:connect:client"))
  implementation(project(":module:capital:charge-card:client"))
  implementation(project(":module:capital:credit:client"))
  implementation(project(":module:capital:account:client"))
  implementation(project(":module:capital:transaction:client"))
  implementation(project(":module:highbeam:client"))
  implementation(project(":module:user:client"))
  //TODO (shubham) Remove once charge card is completely separated from credit
  implementation(project(":module:capital:account:module"))
  implementation(project(":module:capital:charge-card:module"))
  implementation(project(":module:capital:transaction:module"))
  implementation(project(":module:capital:onboarding:module"))
  implementation(project(":module:capital:repayment:module"))
  implementation(project(":module:capital:repayment:client"))
  implementation(project(":module:treasury:client"))

  implementation(Dependencies.Apache.commonsCsv)
  implementation(Dependencies.Pdf.pdf)
  implementation(Dependencies.Ktor.serverMustache)
  implementation(Dependencies.Ktor.serverHtmlBuilder)
  implementation(Dependencies.Gcp.storage)
  implementation(Dependencies.Math.deCampoXirr)
  implementation(Dependencies.Math.kotlinStatistics)

  testImplementation(project(":common:event:testing"))
  testImplementation(project(":common:feature:feature-flags-testing"))
  testImplementation(project(":module:capital:account:testing"))
  testImplementation(project(":module:capital:transaction:testing"))
  testImplementation(project(":module:capital:charge-card:testing"))
  testImplementation(project(":module:capital:repayment:testing"))
  testImplementation(project(":common:integration-testing"))
  testImplementation(project(":common:job-runner:testing"))
  testImplementation(project(":common:rest-feature:testing"))
  testImplementation(project(":common:sql:testing"))
  testImplementation(project(":common:task:testing"))
  testImplementation(project(":db:highbeam"))
  testImplementation(project(":module:capital:credit:client"))
  testImplementation(project(":module:highbeam:client"))
  testImplementation(Dependencies.Testing.junitParams)
}
