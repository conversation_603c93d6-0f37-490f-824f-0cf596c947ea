package co.highbeam.config

import co.highbeam.capital.chargeCard.config.ChargeCardConfig
import co.highbeam.capital.transaction.config.CapitalTransactionConfig
import com.taktile.config.TaktileConfig
import highbeam.config.task.TaskConfig

internal data class CreditFeatureTestConfig(
  val highbeamDatabase: SqlDatabaseConfig,
  val email: EmailConfig,
  val credit: CreditConfig,
  val chargeCard: ChargeCardConfig,
  val creditTransaction: CapitalTransactionConfig,
  val taktile: TaktileConfig,
  val task: TaskConfig,
) : TestConfig()
