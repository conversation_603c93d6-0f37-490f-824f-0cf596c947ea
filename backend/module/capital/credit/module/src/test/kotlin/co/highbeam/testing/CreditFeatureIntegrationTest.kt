package co.highbeam.testing

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.lineOfCredit.LineOfCreditApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.CapitalAccountSummaryClient
import co.highbeam.capital.account.CapitalOfferClient
import co.highbeam.capital.account.FakeCapitalAccountClient
import co.highbeam.capital.account.FakeCapitalAccountSummaryClient
import co.highbeam.capital.account.HttpCapitalAccountClient
import co.highbeam.capital.account.HttpCapitalOfferClient
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.api.CapitalOfferApi
import co.highbeam.capital.account.feature.CapitalAccountFeature
import co.highbeam.capital.account.rep.CapitalAccountControlsRep
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.rep.CapitalAccountSummaryRep
import co.highbeam.capital.account.rep.CapitalLender
import co.highbeam.capital.account.rep.CapitalOfferRep
import co.highbeam.capital.chargeCard.ChargeCardAccountClient
import co.highbeam.capital.chargeCard.ChargeCardSummaryClient
import co.highbeam.capital.chargeCard.feature.ChargeCardFeature
import co.highbeam.capital.onboarding.rep.signatory.SignatoryV1
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementTerms
import co.highbeam.capital.repayment.rep.CapitalRepaymentOption
import co.highbeam.capital.transaction.CapitalTransactionSummaryClient
import co.highbeam.capital.transaction.feature.CapitalTransactionFeature
import co.highbeam.chargeCard.FakeChargeCardSummaryClient
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessAddress.BusinessAddressClient
import co.highbeam.client.businessDetails.BusinessDetailsClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.creditComparison.CreditComparisonClient
import co.highbeam.client.documents.DocumentsClient
import co.highbeam.client.lineOfCredit.LineOfCreditClient
import co.highbeam.client.lineOfCredit.interestFee.LineOfCreditInterestFeeCalculationTaskClient
import co.highbeam.client.lineOfCredit.interestFee.LineOfCreditInterestFeeClient
import co.highbeam.client.lineOfCredit.interestFee.LineOfCreditInterestFeeCompletionTaskClient
import co.highbeam.client.lineOfCredit.reporting.LineOfCreditBusinessReportingClient
import co.highbeam.client.lineOfCredit.reporting.LineOfCreditReportingClient
import co.highbeam.client.lineOfCredit.risk.CreditAdverseActionClient
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.client.onboarding.CreditAgreementClient
import co.highbeam.client.onboarding.CreditApplicationClient
import co.highbeam.client.rutter.InternalRutterPayoutClient
import co.highbeam.client.rutter.RutterConnectionClient
import co.highbeam.client.shopify.ShopifyConnectionClient
import co.highbeam.client.shopify.ShopifyPayoutClient
import co.highbeam.client.treasury.TreasuryRuleClient
import co.highbeam.client.user.UserClient
import com.taktile.client.TaktileClient
import co.highbeam.client.userRole.UserRoleClient
import co.highbeam.client.userRole.UserRoleMembershipClient
import co.highbeam.config.ConfigLoader
import co.highbeam.config.CreditFeatureTestConfig
import co.highbeam.email.EmailService
import co.highbeam.event.FakeEventFeature
import co.highbeam.feature.credit.CreditFeature
import co.highbeam.feature.googleCloudStorage.GoogleCloudStorage
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.lineOfCredit.LineOfCreditRep
import co.highbeam.rep.lineOfCredit.LineOfCreditSetupRep
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import co.highbeam.util.time.inUTC
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoCreditAccountRep
import co.unit.rep.UnitCoCreditAccountRepaymentInformation
import co.unit.rep.UnitCoTransactionRep
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.api.client.googleapis.auth.oauth2.GooglePublicKeysManager
import com.google.common.io.Resources
import com.slack.client.SlackMessageClient
import highbeam.feature.task.TestTaskFeature
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.math.max
import kotlin.math.min

@ExtendWith(CreditFeatureIntegrationTest.Extension::class)
internal abstract class CreditFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {

  private class MockFeature(
    val fakeFeatureFlagService: FakeFeatureFlagService,
    val fakeCreditAccountSummaryClient: FakeCapitalAccountSummaryClient,
    val fakeChargeCardSummaryClient: FakeChargeCardSummaryClient,
  ) : AbstractMockFeature() {
    override fun bind() {
      mock(BankAccountClient::class)
      mock(BusinessAddressClient::class)
      mock(BusinessClient::class)
      mock(BusinessDetailsClient::class)
      mock(BusinessMemberClient::class, mockk(relaxed = true))
      mock(ChargeCardAccountClient::class)
      mock(CapitalAccountClient::class, fakeCreditAccountSummaryClient.capitalAccountClient)
      mock(CapitalAccountSummaryClient::class, fakeCreditAccountSummaryClient)
      mock(EmailService::class)
      mock(FeatureFlagService::class, fakeFeatureFlagService)
      mock(GoogleCloudStorage::class, mockk(relaxUnitFun = true))
      mock(InternalRutterPayoutClient::class)
      mock(LineOfCreditClient::class)
      mock(LineOfCreditInterestFeeClient::class)
      mock(LineOfCreditTransactionsClient::class)
      mock(ShopifyConnectionClient::class)
      mock(ShopifyPayoutClient::class)
      mock(SlackMessageClient::class, mockk(relaxUnitFun = true))
      mock(RutterConnectionClient::class)
      mock(TaktileClient::class)
      mock(
        CapitalTransactionSummaryClient::class,
        fakeCreditAccountSummaryClient.transactionSummaryClient
      )
      mock(TreasuryRuleClient::class)
      mock(UnitCoClient::class)
      mock(UserClient::class)
      mock(UserRoleClient::class, mockk(relaxed = true))
      mock(UserRoleMembershipClient::class, mockk(relaxed = true))
      mock(ChargeCardSummaryClient::class, fakeChargeCardSummaryClient)
      mock(GooglePublicKeysManager::class)
    }
  }

  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val config: CreditFeatureTestConfig = ConfigLoader.load("test")
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "credit",
      )
      val fakeCreditAccountSummaryClient = FakeCapitalAccountSummaryClient()
      val fakeChargeCardSummaryClient = FakeChargeCardSummaryClient()
      val fakeFeatureFlagService = FakeFeatureFlagService()
      val eventFeature = FakeEventFeature
      val taskFeature = TestTaskFeature(config.task)
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<CreditFeatureTestConfig>(config) {
          override val features = setOf(
            TestRestFeature(),
            sqlFeature,
            eventFeature,
            CapitalAccountFeature(),
            CreditFeature(credit = config.credit, taktile = config.taktile),
            CapitalTransactionFeature(config.creditTransaction),
            ChargeCardFeature(config.chargeCard),
            MockFeature(
              fakeFeatureFlagService = fakeFeatureFlagService,
              fakeCreditAccountSummaryClient = fakeCreditAccountSummaryClient,
              fakeChargeCardSummaryClient = fakeChargeCardSummaryClient,
            ),
            taskFeature,
          )
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      val injector = context[Server::class.java].injector
      sqlFeature.truncateSchema(injector)
      fakeFeatureFlagService.reset()
      fakeCreditAccountSummaryClient.reset()
      eventFeature.reset()
      taskFeature.reset(injector)
    }

    override fun stop() {
      sharedState.stop()
    }
  }

  val capitalAccountClient: CapitalAccountClient by lazy {
    HttpCapitalAccountClient(httpClient)
  }

  val documentsClient: DocumentsClient by lazy {
    DocumentsClient(httpClient)
  }

  val lineOfCreditClient: LineOfCreditClient by lazy {
    LineOfCreditClient(httpClient)
  }

  val lineOfCreditInterestFeeClient: LineOfCreditInterestFeeClient by lazy {
    LineOfCreditInterestFeeClient(httpClient)
  }

  val lineOfCreditInterestFeeCalculationTaskClient: LineOfCreditInterestFeeCalculationTaskClient
    by lazy {
      LineOfCreditInterestFeeCalculationTaskClient(httpClient)
    }

  val lineOfCreditInterestFeeCompletionTaskClient: LineOfCreditInterestFeeCompletionTaskClient
    by lazy {
      LineOfCreditInterestFeeCompletionTaskClient(httpClient)
    }

  val lineOfCreditBusinessReportingClient: LineOfCreditBusinessReportingClient by lazy {
    LineOfCreditBusinessReportingClient(httpClient)
  }

  val lineOfCreditReportingClient: LineOfCreditReportingClient by lazy {
    LineOfCreditReportingClient(httpClient)
  }

  val lineOfCreditTransactionClient: LineOfCreditTransactionsClient by lazy {
    LineOfCreditTransactionsClient(httpClient)
  }

  val creditAdverseActionClient: CreditAdverseActionClient by lazy {
    CreditAdverseActionClient(httpClient)
  }

  val creditComparisonClient: CreditComparisonClient by lazy {
    CreditComparisonClient(httpClient)
  }

  val creditAgreementClient: CreditAgreementClient by lazy {
    CreditAgreementClient(httpClient)
  }

  val creditApplicationClient: CreditApplicationClient by lazy {
    CreditApplicationClient(httpClient)
  }

  val capitalOfferClient: CapitalOfferClient by lazy {
    HttpCapitalOfferClient(httpClient)
  }

  protected fun mockBusinessClient(businessGuid: UUID): BusinessRep.Complete {
    val business = BusinessRep.Complete(
      guid = businessGuid,
      dba = null,
      name = "Highbeam",
      referralLinkGuid = null,
      ownerUserGuid = UUID.randomUUID(),
      unitCoCustomerId = "431742",
      status = BusinessRep.Complete.Status.Active,
      stateOfIncorporation = BusinessRep.StateOfIncorporation.NY,
      naics = null,
    )
    coEvery { get<BusinessClient>().request(BusinessApi.Get(businessGuid)) } returns business

    return business
  }

  protected fun mockUnitCoTransactionClient(
    path: String = "transaction/happy.json"
  ) {
    coEvery {
      get<UnitCoClient>().transaction.list(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )
    } returns flowOf(
      objectMapper.readValue(
        Resources.getResource(path)
      )
    )
  }

  protected fun mockUnitCoTransactionClientInterestPayment(
    path: String = "transaction/happy.json"
  ) {
    coEvery {
      get<UnitCoClient>().transaction.list(
        any(),
        any(),
        any(),
        any(),
        any(),
        tags = mapOf(
          "transactionType" to
            UnitCoTransactionRep.TransactionType.LineOfCreditInterestPayment.value
        )
      )
    } returns flowOf(
      objectMapper.readValue(
        Resources.getResource(path)
      )
    )
  }

  protected fun mockUnitCoTransactionClient(
    path: String = "transaction/happy.json",
    creditAccountGuid: UUID,
  ) {
    coEvery {
      get<UnitCoClient>().transaction.list(
        any(),
        any(),
        any(),
        any(),
        any(),
        tags = mapOf("creditAccountGuid" to creditAccountGuid.toString())
      )
    } returns flowOf(
      objectMapper.readValue(
        Resources.getResource(path)
      )
    )
  }

  protected fun mockGetBankAccount(
    bankAccountGuid: UUID,
    bankAccount: BankAccountRep.Complete?,
  ) {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.Get(accountGuid = bankAccountGuid)
      )
    } returns bankAccount
  }

  protected fun mockGetPrimaryBankAccountByBusinessGuid(
    businessGuid: UUID,
    bankAccount: BankAccountRep.Complete?
  ) {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid = businessGuid)
      )
    } returns bankAccount
  }

  protected fun mockTwoBusinessGetAll(
    businessGuid1: UUID,
    businessGuid2: UUID,
  ) {
    coEvery { get<BusinessClient>().request(BusinessApi.GetAll) } returns
      listOf(
        BusinessRep.Complete(
          guid = businessGuid1,
          dba = null,
          name = "Highbeam1",
          referralLinkGuid = null,
          ownerUserGuid = UUID.randomUUID(),
          unitCoCustomerId = "1111",
          status = BusinessRep.Complete.Status.Active,
          stateOfIncorporation = BusinessRep.StateOfIncorporation.NY,
          naics = null,
        ),
        BusinessRep.Complete(
          guid = businessGuid2,
          dba = null,
          name = "Highbeam2",
          referralLinkGuid = null,
          ownerUserGuid = UUID.randomUUID(),
          unitCoCustomerId = "2222",
          status = BusinessRep.Complete.Status.Active,
          stateOfIncorporation = BusinessRep.StateOfIncorporation.NY,
          naics = null,
        )
      )
  }

  protected fun mockUnitGetCreditAccount(
    businessName: String = "Amazing test business",
    businessGuid: UUID,
    available: Balance,
    hold: Balance = Balance.ZERO,
    creditLimit: Money,
    unitCoCreditAccountId: String
  ) {
    val createdCreditAccount = UnitCoCreditAccountRep.Complete(
      id = unitCoCreditAccountId,
      name = "$businessName credit account",
      businessGuid = businessGuid,
      hold = hold,
      balance = Balance(creditLimit.rawCents) - available - hold,
      available = available,
      creditLimit = creditLimit,
      status = UnitCoCreditAccountRep.Status.Open,
      type = "creditAccount",
      creditTerms = "highbeam_sandbox_credit_terms"
    )
    coEvery {
      get<UnitCoClient>().creditAccount.get(unitCoCreditAccountId)
    } returns createdCreditAccount
  }

  protected fun mockUnitCreateCreditAccount(
    businessName: String,
    businessGuid: UUID,
    creditAccountId: String = UUID.randomUUID().toString(),
    creditLimit: Money,
    unitCoCustomerId: String,
    status: UnitCoCreditAccountRep.Status = UnitCoCreditAccountRep.Status.Open,
  ) {
    val createdCreditAccount = UnitCoCreditAccountRep.Complete(
      id = creditAccountId,
      name = "$businessName credit account",
      businessGuid = businessGuid,
      hold = Balance.ZERO,
      balance = Balance.ZERO,
      available = Balance(creditLimit.rawCents),
      creditLimit = creditLimit,
      status = status,
      type = "creditAccount",
      creditTerms = "highbeam_sandbox_credit_terms",
    )
    coEvery {
      get<UnitCoClient>().creditAccount.create(
        UnitCoCreditAccountRep.Creator(
          name = "$businessName credit account",
          businessGuid = businessGuid,
          creditLimit = creditLimit,
          customerId = unitCoCustomerId,
          creditTerms = "highbeam_sandbox_credit_terms",
        )
      )
    } returns createdCreditAccount
  }

  protected fun mockUnitRepaymentInformation(
    creditAccountId: String,
    remainingAmountDue: Money = Money(0),
    remainingAmountOverdue: Money = Money(0),
    initiatedRepayments: Money = Money(0),
    statementPeriodStart: LocalDate = LocalDate.now(clock).withDayOfMonth(1),
    nextRepaymentDueDate: LocalDate = statementPeriodStart.plusMonths(1).withDayOfMonth(15),
  ) {
    val repaymentInformation = UnitCoCreditAccountRepaymentInformation.Complete(
      remainingAmountDue = remainingAmountDue,
      remainingAmountOverdue = remainingAmountOverdue,
      statementPeriodStart = statementPeriodStart,
      initiatedRepayments = initiatedRepayments,
      statementPeriodEnd = statementPeriodStart.plusMonths(1).withDayOfMonth(1).minusDays(1),
      nextRepaymentDueDate = nextRepaymentDueDate
    )
    coEvery {
      get<UnitCoClient>().creditAccount.getRepaymentInformation(
        accountId = creditAccountId,
      )
    } returns repaymentInformation
  }

  // TODO: This is a stop gap until we break out the capital limit logic into its own service
  protected fun calculateAvailableLineOfCreditLimit(
    lineOfCredit: CapitalAccountSummaryRep
  ) = Money(
    // Max prevents a negative credit limit
    max(
      lineOfCredit.details.limit.rawCents + min(lineOfCredit.runningBalance.rawCents, 0),
      0,
    )
  )

  protected fun bankAccountRep(
    guid: UUID = UUID.randomUUID(),
    businessGuid: UUID,
    unitCoDepositAccountId: String,
    isPrimary: Boolean,
    name: String,
    highbeamType: BankAccountRep.Type,
    availableBalance: Balance = Balance.ZERO,
    depositProduct: DepositAccountRep.DepositProduct =
      highbeamType.supportedUnitCoDepositProducts.first(),
    unitCoCounterpartyId: String? = null,
  ): BankAccountRep.Complete =
    BankAccountRep.Complete(
      guid = guid,
      unitCoDepositAccountId = unitCoDepositAccountId,
      businessGuid = businessGuid,
      name = name,
      unitCoCounterpartyId = unitCoCounterpartyId,
      status = BankAccountRep.Status.OPEN,
      isPrimary = isPrimary,
      availableBalance = availableBalance,
      routingNumber = "123",
      accountNumber = "456",
      type = "depositAccount",
      highbeamType = highbeamType,
      depositProduct = depositProduct,
      minimumRequiredBalance = Balance.ZERO,
    )

  protected suspend fun setUpLineOfCredit(
    businessGuid: UUID,
    userGuid: UUID = UUID.randomUUID(),
    limit: Long = 100_000_00L,
    apr: BigDecimal = BigDecimal("0.15"),
    remittanceRate: BigDecimal = BigDecimal("0.12"),
    bankAccountGuid: UUID = UUID.randomUUID(),
  ): UUID {
    coEvery {
      get<TreasuryRuleClient>()(TreasuryRuleApi.GetForBusiness(businessGuid))
    } returns emptyList()

    every {
      get<GoogleCloudStorage>().upload(any(), any())
    } returns mockk()

    val lineOfCreditGuid = lineOfCreditClient.request(
      LineOfCreditApi.SetUpLineOfCredit(
        businessGuid = businessGuid,
        rep = LineOfCreditSetupRep(
          terms = CapitalAgreementTerms.DrawPeriodV1(
            limit = Money(limit),
            apr = apr,
            repaymentTermInDays = 120,
            businessName = "Justin's Jamba Juices",
            businessIncorporationState = "NY",
            signatory = SignatoryV1(
              positionTitle = "CEO",
              fullName = "Justin McKibben",
              email = "<EMAIL>",
              noticeAddress = "111 12th St. New York, NY 10000",
            ),
            drawPeriodEndsAt = ZonedDateTime.now(clock).plusYears(1).inUTC(),
            date = LocalDate.now(clock),
          ),
        )
      )
    )
    lineOfCreditClient.request(
      LineOfCreditApi.Update(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
        rep = LineOfCreditRep.Updater(
        )
      )
    )

    capitalOfferClient(
      CapitalOfferApi.Create(
        businessGuid = businessGuid,
        capitalAccountGuid = lineOfCreditGuid,
        creator = CapitalOfferRep.Creator(),
      )
    )

    lineOfCreditClient.request(
      LineOfCreditApi.AcceptOffer(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
        userGuid = userGuid,
      )
    )

    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid)
      )
    } returns mockk { every { <EMAIL> } returns uuidGenerator[1001] }

    capitalAccountClient(
      CapitalAccountApi.Activate(guid = lineOfCreditGuid, businessGuid = businessGuid)
    )

    val fakeCapitalAccountClient = get<CapitalAccountClient>() as FakeCapitalAccountClient
    fakeCapitalAccountClient.add(
      CapitalAccountRep(
        name = "Highbeam Card",
        type = CapitalAccountRep.Type.CashAccessOnly,
        guid = lineOfCreditGuid,
        businessGuid = businessGuid,
        details = CapitalAccountDetailsRep(
          limit = Money.fromCents(limit),
          apr = apr,
          repayment = CapitalAccountDetailsRep.Repayment(
            option = CapitalRepaymentOption.PayoutPercentage(remittanceRate),
            bankAccountGuid = bankAccountGuid,
          ),
          lineType = CapitalAccountDetailsRep.LineType.Revolving,
          targetRepaymentDays = 120,
          securedStatus = CapitalAccountDetailsRep.SecuredStatus.Unsecured,
        ),
        state = CapitalAccountRep.State.Active,
        controls = CapitalAccountControlsRep(
          drawdownEnabled = true,
        ),
        activatedAt = clock.date().inUTC(),
        lender = CapitalLender.Highbeam,
        terminatedAt = null,
      )
    )

    return lineOfCreditGuid
  }
}
