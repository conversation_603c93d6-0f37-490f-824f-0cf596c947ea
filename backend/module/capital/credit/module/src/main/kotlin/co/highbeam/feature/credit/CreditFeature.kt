package co.highbeam.feature.credit

import co.highbeam.config.CreditConfig
import co.highbeam.endpoint.creditComparison.CalculateCreditComparisonSnapshot
import com.taktile.config.TaktileConfig
import co.highbeam.endpoint.creditComparison.CreateCreditComparisonSession
import co.highbeam.endpoint.creditComparison.CreateCreditComparisonSnapshot
import co.highbeam.endpoint.creditComparison.GetLatestCreditComparisonSnapshot
import co.highbeam.endpoint.creditComparison.GetRevenueForecast
import co.highbeam.endpoint.creditComparison.UpdateCreditComparisonSession
import co.highbeam.endpoint.documents.CreateDocument
import co.highbeam.endpoint.documents.GetDocumentSignedUrl
import co.highbeam.endpoint.documents.GetDocuments
import co.highbeam.endpoint.lineOfCredit.CreateLineOfCredit
import co.highbeam.endpoint.lineOfCredit.GetAvailableFinancing
import co.highbeam.endpoint.lineOfCredit.GetLineOfCreditHistoricalDetails
import co.highbeam.endpoint.lineOfCredit.UpdateLineOfCredit
import co.highbeam.endpoint.lineOfCredit.agreement.GetLineOfCreditAgreements
import co.highbeam.endpoint.lineOfCredit.agreement.GetLineOfCreditAgreementsMetadata
import co.highbeam.endpoint.lineOfCredit.interestFee.CalculateInterestFees
import co.highbeam.endpoint.lineOfCredit.interestFee.CompleteInterestFees
import co.highbeam.endpoint.lineOfCredit.interestFee.CreateLineOfCreditInterestFeeCalculationTask
import co.highbeam.endpoint.lineOfCredit.interestFee.CreateLineOfCreditInterestFeeCompletionTask
import co.highbeam.endpoint.lineOfCredit.interestFee.ExecuteLineOfCreditInterestFeeCalculationTask
import co.highbeam.endpoint.lineOfCredit.interestFee.ExecuteLineOfCreditInterestFeeCompletionTask
import co.highbeam.endpoint.lineOfCredit.interestFee.GetAccruedInterestFees
import co.highbeam.endpoint.lineOfCredit.interestFee.RetryFailedInterestFee
import co.highbeam.endpoint.lineOfCredit.interestFee.RetryFailedInterestFees
import co.highbeam.endpoint.lineOfCredit.reporting.GetCreditAging
import co.highbeam.endpoint.lineOfCredit.reporting.GetLineOfCreditAgingReport
import co.highbeam.endpoint.lineOfCredit.reporting.GetLineOfCreditHistoryReport
import co.highbeam.endpoint.lineOfCredit.reporting.GetLoanBookSummary
import co.highbeam.endpoint.lineOfCredit.reporting.GetMatchedAgingReport
import co.highbeam.endpoint.lineOfCredit.risk.CreateAdverseAction
import co.highbeam.endpoint.lineOfCredit.risk.NotifyAdverseAction
import co.highbeam.endpoint.lineOfCredit.transaction.CreateDrawdown
import co.highbeam.endpoint.lineOfCredit.transaction.CreateRepayment
import co.highbeam.endpoint.lineOfCredit.transaction.ForceDrawdown
import co.highbeam.endpoint.lineOfCredit.transaction.LineOfCreditDetailedTransactions
import co.highbeam.endpoint.lineOfCredit.transaction.LineOfCreditTransactions
import co.highbeam.endpoint.lineOfCredit.transaction.LineOfCreditTransactionsCsv
import co.highbeam.endpoint.onboarding.AcceptLineOfCreditOffer
import co.highbeam.endpoint.onboarding.SetupLineOfCredit
import co.highbeam.endpoint.onboarding.creditAgreement.GenerateLineOfCreditAgreement
import co.highbeam.endpoint.onboarding.creditAgreement.GetOnboardingLineOfCreditAgreements
import co.highbeam.endpoint.onboarding.creditAgreement.RegenerateSignedAgreement
import co.highbeam.endpoint.onboarding.creditAgreement.UpdateLineOfCreditAgreementsMetadata
import co.highbeam.endpoint.onboarding.creditAgreement.UpdateLineOfCreditUserActionsMetadata
import co.highbeam.endpoint.onboarding.creditApplication.CreateCreditApplication
import co.highbeam.endpoint.onboarding.creditApplication.CreateCreditApplicationDocument
import co.highbeam.endpoint.onboarding.creditApplication.DeleteCreditApplicationDocument
import co.highbeam.endpoint.onboarding.creditApplication.GetCreditApplication
import co.highbeam.endpoint.onboarding.creditApplication.GetCreditApplicationDocuments
import co.highbeam.endpoint.onboarding.creditApplication.GetCreditApplicationsByBusiness
import co.highbeam.endpoint.onboarding.creditApplication.SubmitCreditApplication
import co.highbeam.endpoint.onboarding.creditApplication.UpdateCreditApplication
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.feature.Feature
import co.highbeam.listener.lineOfCredit.LineOfCreditApplicationApprovedListener
import co.highbeam.listener.lineOfCredit.LineOfCreditInterestTransferListener
import co.highbeam.publisher.lineOfCredit.CreditApplicationApprovedPublisherFactory
import co.highbeam.publisher.lineOfCredit.LineOfCreditActivationPublisherFactory
import co.highbeam.publisher.lineOfCredit.LineOfCreditInterestTransferPublisherFactory
import com.google.inject.Injector
import com.google.inject.Key
import com.google.inject.TypeLiteral

class CreditFeature(
  val credit: CreditConfig,
  val taktile: TaktileConfig,
) : Feature() {
  private val publishers: MutableSet<TypeLiteral<out EventPublisher<*>>> = mutableSetOf()
  override fun bind() {
    bindConfigs()
    bindApiEndpoints()
    bindPublishers()
    bindListeners()
  }

  override fun stop(injector: Injector) {
    publishers.forEach { injector.getInstance(Key.get(it)).close() }
  }

  private fun bindConfigs() {
    bind(CreditConfig::class.java).toInstance(credit)
    bind(TaktileConfig::class.java).toInstance(taktile)
  }

  private fun bindPublishers() {
    LineOfCreditInterestTransferPublisherFactory.bind(binder(), publishers)
    CreditApplicationApprovedPublisherFactory.bind(binder(), publishers)
    LineOfCreditActivationPublisherFactory.bind(binder(), publishers)
  }

  private fun bindListeners() {
    bind(LineOfCreditApplicationApprovedListener::class.java)
    bind(LineOfCreditInterestTransferListener::class.java)
  }

  private fun bindApiEndpoints() {
    bind(AcceptLineOfCreditOffer::class.java).asEagerSingleton()
    bind(CalculateCreditComparisonSnapshot::class.java).asEagerSingleton()
    bind(CalculateInterestFees::class.java).asEagerSingleton()
    bind(CompleteInterestFees::class.java).asEagerSingleton()
    bind(CreateAdverseAction::class.java).asEagerSingleton()
    bind(CreateCreditApplication::class.java).asEagerSingleton()
    bind(CreateCreditApplicationDocument::class.java).asEagerSingleton()
    bind(CreateCreditComparisonSession::class.java).asEagerSingleton()
    bind(CreateCreditComparisonSnapshot::class.java).asEagerSingleton()
    bind(CreateDocument::class.java).asEagerSingleton()
    bind(CreateDrawdown::class.java).asEagerSingleton()
    bind(CreateLineOfCredit::class.java).asEagerSingleton()
    bind(CreateLineOfCreditInterestFeeCalculationTask::class.java).asEagerSingleton()
    bind(CreateLineOfCreditInterestFeeCompletionTask::class.java).asEagerSingleton()
    bind(CreateRepayment::class.java).asEagerSingleton()
    bind(DeleteCreditApplicationDocument::class.java).asEagerSingleton()
    bind(ExecuteLineOfCreditInterestFeeCalculationTask::class.java).asEagerSingleton()
    bind(ExecuteLineOfCreditInterestFeeCompletionTask::class.java).asEagerSingleton()
    bind(ForceDrawdown::class.java).asEagerSingleton()
    bind(GenerateLineOfCreditAgreement::class.java).asEagerSingleton()
    bind(GetAccruedInterestFees::class.java).asEagerSingleton()
    bind(GetAvailableFinancing::class.java).asEagerSingleton()
    bind(GetCreditAging::class.java).asEagerSingleton()
    bind(GetCreditApplication::class.java).asEagerSingleton()
    bind(GetCreditApplicationDocuments::class.java).asEagerSingleton()
    bind(GetCreditApplicationsByBusiness::class.java).asEagerSingleton()
    bind(GetDocuments::class.java).asEagerSingleton()
    bind(GetDocumentSignedUrl::class.java).asEagerSingleton()
    bind(GetLatestCreditComparisonSnapshot::class.java).asEagerSingleton()
    bind(GetLineOfCreditAgingReport::class.java).asEagerSingleton()
    bind(GetLineOfCreditAgreements::class.java).asEagerSingleton()
    bind(GetLineOfCreditAgreementsMetadata::class.java).asEagerSingleton()
    bind(GetLineOfCreditHistoricalDetails::class.java).asEagerSingleton()
    bind(GetLineOfCreditHistoryReport::class.java).asEagerSingleton()
    bind(GetLoanBookSummary::class.java).asEagerSingleton()
    bind(GetMatchedAgingReport::class.java).asEagerSingleton()
    bind(GetOnboardingLineOfCreditAgreements::class.java).asEagerSingleton()
    bind(GetRevenueForecast::class.java).asEagerSingleton()
    bind(LineOfCreditDetailedTransactions::class.java).asEagerSingleton()
    bind(LineOfCreditTransactions::class.java).asEagerSingleton()
    bind(LineOfCreditTransactionsCsv::class.java).asEagerSingleton()
    bind(NotifyAdverseAction::class.java).asEagerSingleton()
    bind(RegenerateSignedAgreement::class.java).asEagerSingleton()
    bind(RetryFailedInterestFee::class.java).asEagerSingleton()
    bind(RetryFailedInterestFees::class.java).asEagerSingleton()
    bind(SetupLineOfCredit::class.java).asEagerSingleton()
    bind(SubmitCreditApplication::class.java).asEagerSingleton()
    bind(UpdateCreditApplication::class.java).asEagerSingleton()
    bind(UpdateCreditComparisonSession::class.java).asEagerSingleton()
    bind(UpdateLineOfCredit::class.java).asEagerSingleton()
    bind(UpdateLineOfCreditAgreementsMetadata::class.java).asEagerSingleton()
    bind(UpdateLineOfCreditUserActionsMetadata::class.java).asEagerSingleton()
  }
}
